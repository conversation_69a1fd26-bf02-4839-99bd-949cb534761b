@import url('https://fonts.googleapis.com/css2?family=Noto+Naskh+Arabic:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --text-color: #ffffff;
  --bg-color: #121212;
  --heading-1-size: 2.5rem;
  --heading-2-size: 2rem;
  --heading-3-size: 1.5rem;
  --heading-line-height: 1.4;
  background-color: var(--bg-color);
  color: var(--text-color);
}

body {
  font-family: 'Inter', sans-serif;
  margin: 0;
  padding: 0;
}

.font-arabic {
  font-family: 'Noto Naskh Arabic', serif;
}

/* Base Typography Styles */
h1 {
  font-size: var(--heading-1-size);
  line-height: var(--heading-line-height);
  font-weight: 700;
  margin: 1.5rem 0 1rem;
}

h2 {
  font-size: var(--heading-2-size);
  line-height: var(--heading-line-height);
  font-weight: 600;
  margin: 1.25rem 0 0.75rem;
}

h3 {
  font-size: var(--heading-3-size);
  line-height: var(--heading-line-height);
  font-weight: 500;
  margin: 1rem 0 0.5rem;
}

/* Custom styles for center and right aligned content */
.center-aligned {
  display: block;
  text-align: center;
  margin: 1.5rem 0;
}

.right-aligned {
  display: block;
  text-align: right;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  :root {
    --heading-1-size: 2rem;
    --heading-2-size: 1.5rem;
    --heading-3-size: 1.25rem;
  }
}

/* Styles for Risale Content Tags */
.risale-content ca {
  display: block; /* Ensure it takes full width */
  text-align: center; /* Center the text */
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  font-family: 'Noto Naskh Arabic', serif; /* Apply Arabic font */
}

.risale-content ra {
  display: block; /* Ensure it takes full width */
  text-align: right; /* Right-align the text */
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  font-family: 'Noto Naskh Arabic', serif; /* Apply Arabic font */
}

/* Ensure standard tags are styled correctly within Risale content */
.risale-content b {
  font-weight: bold;
}

.risale-content i {
  font-style: italic;
}

/* Interactive spans for dictionary/footnotes */
.interactive-word,
.interactive-footnote {
  cursor: pointer;
  text-decoration: underline;
  text-decoration-style: dotted;
  text-decoration-color: var(--text-color); /* Use theme color */
  text-underline-offset: 2px; /* Original offset */
  transition: color 0.2s ease;
}

/* Interactive spans for Arabic phrases - with adjusted underline offset */
.interactive-arabic-phrase {
  cursor: pointer;
  text-decoration: underline;
  text-decoration-style: dotted;
  text-decoration-color: var(--text-color); /* Use theme color */
  text-underline-offset: 6px; /* Increased offset for Arabic phrases further */
  transition: color 0.2s ease;
  font-size: 170%; /* Increase font size only for Arabic phrases */
}

/* Hover effect for all interactive elements */
.interactive-word:hover,
.interactive-footnote:hover,
.interactive-arabic-phrase:hover {
  color: color-mix(in srgb, var(--text-color) 70%, transparent); /* Slightly dimmed */
}

/* Tooltip Component Style */
.tooltip-component {
  background-color: color-mix(in srgb, var(--bg-color) 90%, #ffffff); /* Slightly lighter/darker than bg */
  color: var(--text-color);
  padding: 0.6rem 0.9rem;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  max-width: 300px; /* Adjust as needed */
  font-size: 0.9rem;
  line-height: 1.5;
  border: 1px solid color-mix(in srgb, var(--text-color) 20%, transparent);
}

.tooltip-component p { /* Ensure paragraphs inside tooltip have reasonable margin */
  margin-bottom: 0.5em;
}
.tooltip-component p:last-child {
  margin-bottom: 0;
}

/* RGB Color Picker Slider Styles */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  height: 8px;
  border-radius: 4px;
  outline: none;
  cursor: pointer;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #ffffff;
  border: 2px solid #333333;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #ffffff;
  border: 2px solid #333333;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input[type="range"]::-webkit-slider-track {
  height: 8px;
  border-radius: 4px;
}

input[type="range"]::-moz-range-track {
  height: 8px;
  border-radius: 4px;
  border: none;
  outline: none;
} 