import React, { useState, useCallback, useEffect } from 'react';
import { Palette, RotateCcw } from 'lucide-react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

interface RGBColorPickerProps {
  /** Se<PERSON>ili renk (hex formatında) */
  selectedColor?: string;
  /** Renk seçildiğinde tetiklenen callback */
  onColorChange: (color: string) => void;
  /** Vars<PERSON><PERSON>lan renk */
  defaultColor?: string;
  /** Başlık */
  title?: string;
  /** Compact görünüm */
  compact?: boolean;
}

interface RGBValues {
  r: number;
  g: number;
  b: number;
}

const RGBColorPicker: React.FC<RGBColorPickerProps> = ({
  selectedColor,
  onColorChange,
  defaultColor = '#ffffff',
  title = 'Renk Seçici',
  compact = false
}) => {
  const bgColor = useAutoOverlay(15, 'var(--bg-color)');
  const cardBgColor = useAutoOverlay(8, 'var(--bg-color)');
  const borderColor = useAutoOverlay(12, 'var(--bg-color)');

  // Hex'i RGB'ye çevir
  const hexToRgb = useCallback((hex: string): RGBValues => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : { r: 255, g: 255, b: 255 };
  }, []);

  // RGB'yi hex'e çevir
  const rgbToHex = useCallback((r: number, g: number, b: number): string => {
    const toHex = (n: number) => {
      const hex = Math.round(Math.max(0, Math.min(255, n))).toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    };
    return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
  }, []);

  const [rgbValues, setRgbValues] = useState<RGBValues>(() => 
    hexToRgb(selectedColor || defaultColor)
  );

  // selectedColor değiştiğinde RGB değerlerini güncelle
  useEffect(() => {
    if (selectedColor) {
      setRgbValues(hexToRgb(selectedColor));
    }
  }, [selectedColor, hexToRgb]);

  // RGB değerleri değiştiğinde hex rengi güncelle
  const handleRgbChange = useCallback((component: keyof RGBValues, value: number) => {
    const newRgbValues = { ...rgbValues, [component]: value };
    setRgbValues(newRgbValues);
    
    const hexColor = rgbToHex(newRgbValues.r, newRgbValues.g, newRgbValues.b);
    onColorChange(hexColor);
  }, [rgbValues, rgbToHex, onColorChange]);

  // Varsayılan renge sıfırla
  const handleReset = () => {
    const defaultRgb = hexToRgb(defaultColor);
    setRgbValues(defaultRgb);
    onColorChange(defaultColor);
  };

  // Hex input değişikliği
  const handleHexChange = (hex: string) => {
    if (/^#?([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(hex)) {
      const fullHex = hex.startsWith('#') ? hex : `#${hex}`;
      setRgbValues(hexToRgb(fullHex));
      onColorChange(fullHex);
    }
  };

  const currentHex = rgbToHex(rgbValues.r, rgbValues.g, rgbValues.b);

  return (
    <div 
      className={`rgb-color-picker p-4 rounded-xl border ${compact ? 'space-y-3' : 'space-y-4'}`}
      style={{
        backgroundColor: bgColor,
        borderColor: borderColor
      }}
    >
      {/* Header */}
      <div className="flex items-center gap-2 pb-2 border-b" style={{ borderColor: borderColor }}>
        <Palette size={16} style={{ color: 'var(--text-color)' }} />
        <span className="text-sm font-medium" style={{ color: 'var(--text-color)' }}>
          {title}
        </span>
        <button
          onClick={handleReset}
          className="ml-auto p-1 rounded-md hover:bg-[var(--text-color)]/10 transition-colors"
          style={{ color: 'var(--text-color)' }}
          title="Varsayılan renge sıfırla"
        >
          <RotateCcw size={14} />
        </button>
      </div>

      {/* Color Preview */}
      <div className="flex items-center gap-3">
        <div 
          className="w-12 h-12 rounded-lg border-2 flex-shrink-0"
          style={{ 
            backgroundColor: currentHex,
            borderColor: borderColor
          }}
        />
        <div className="flex-1">
          <div className="text-xs opacity-70 mb-1" style={{ color: 'var(--text-color)' }}>
            Seçili Renk
          </div>
          <input
            type="text"
            value={currentHex}
            onChange={(e) => handleHexChange(e.target.value)}
            className="w-full px-2 py-1 text-sm rounded border font-mono"
            style={{
              backgroundColor: cardBgColor,
              borderColor: borderColor,
              color: 'var(--text-color)'
            }}
            placeholder="#ffffff"
          />
        </div>
      </div>

      {/* RGB Sliders */}
      <div className="space-y-3">
        {/* Red */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="text-xs font-medium opacity-80" style={{ color: 'var(--text-color)' }}>
              Kırmızı (R)
            </label>
            <span className="text-xs font-mono" style={{ color: 'var(--text-color)' }}>
              {rgbValues.r}
            </span>
          </div>
          <div className="relative">
            <input
              type="range"
              min="0"
              max="255"
              value={rgbValues.r}
              onChange={(e) => handleRgbChange('r', parseInt(e.target.value))}
              className="w-full h-2 bg-gradient-to-r from-black to-red-500 rounded-lg appearance-none cursor-pointer"
              style={{
                background: `linear-gradient(to right, rgb(0, ${rgbValues.g}, ${rgbValues.b}), rgb(255, ${rgbValues.g}, ${rgbValues.b}))`
              }}
            />
          </div>
        </div>

        {/* Green */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="text-xs font-medium opacity-80" style={{ color: 'var(--text-color)' }}>
              Yeşil (G)
            </label>
            <span className="text-xs font-mono" style={{ color: 'var(--text-color)' }}>
              {rgbValues.g}
            </span>
          </div>
          <div className="relative">
            <input
              type="range"
              min="0"
              max="255"
              value={rgbValues.g}
              onChange={(e) => handleRgbChange('g', parseInt(e.target.value))}
              className="w-full h-2 bg-gradient-to-r from-black to-green-500 rounded-lg appearance-none cursor-pointer"
              style={{
                background: `linear-gradient(to right, rgb(${rgbValues.r}, 0, ${rgbValues.b}), rgb(${rgbValues.r}, 255, ${rgbValues.b}))`
              }}
            />
          </div>
        </div>

        {/* Blue */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="text-xs font-medium opacity-80" style={{ color: 'var(--text-color)' }}>
              Mavi (B)
            </label>
            <span className="text-xs font-mono" style={{ color: 'var(--text-color)' }}>
              {rgbValues.b}
            </span>
          </div>
          <div className="relative">
            <input
              type="range"
              min="0"
              max="255"
              value={rgbValues.b}
              onChange={(e) => handleRgbChange('b', parseInt(e.target.value))}
              className="w-full h-2 bg-gradient-to-r from-black to-blue-500 rounded-lg appearance-none cursor-pointer"
              style={{
                background: `linear-gradient(to right, rgb(${rgbValues.r}, ${rgbValues.g}, 0), rgb(${rgbValues.r}, ${rgbValues.g}, 255))`
              }}
            />
          </div>
        </div>
      </div>

      {/* RGB Değerleri */}
      <div className="grid grid-cols-3 gap-2">
        <div>
          <label className="text-xs opacity-70 block mb-1" style={{ color: 'var(--text-color)' }}>R</label>
          <input
            type="number"
            min="0"
            max="255"
            value={rgbValues.r}
            onChange={(e) => handleRgbChange('r', parseInt(e.target.value) || 0)}
            className="w-full px-2 py-1 text-xs rounded border text-center"
            style={{
              backgroundColor: cardBgColor,
              borderColor: borderColor,
              color: 'var(--text-color)'
            }}
          />
        </div>
        <div>
          <label className="text-xs opacity-70 block mb-1" style={{ color: 'var(--text-color)' }}>G</label>
          <input
            type="number"
            min="0"
            max="255"
            value={rgbValues.g}
            onChange={(e) => handleRgbChange('g', parseInt(e.target.value) || 0)}
            className="w-full px-2 py-1 text-xs rounded border text-center"
            style={{
              backgroundColor: cardBgColor,
              borderColor: borderColor,
              color: 'var(--text-color)'
            }}
          />
        </div>
        <div>
          <label className="text-xs opacity-70 block mb-1" style={{ color: 'var(--text-color)' }}>B</label>
          <input
            type="number"
            min="0"
            max="255"
            value={rgbValues.b}
            onChange={(e) => handleRgbChange('b', parseInt(e.target.value) || 0)}
            className="w-full px-2 py-1 text-xs rounded border text-center"
            style={{
              backgroundColor: cardBgColor,
              borderColor: borderColor,
              color: 'var(--text-color)'
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default RGBColorPicker;
