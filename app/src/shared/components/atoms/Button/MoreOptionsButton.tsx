import { forwardRef, ButtonHTMLAttributes, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '@shared/context/ThemeContext';
import { Moon, Sun, Coffee, Search, Palette, RotateCcw } from 'lucide-react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { ThemeMode } from '@shared/theme/definitions';
import { useSettingsStore } from '@domains/settings/store/settingsstore';
import { RGBColorPicker } from '@shared/components';

interface MoreOptionsButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  // Add any specific props if needed, otherwise inherit from ButtonHTMLAttributes
  onClick?: () => void; // Keep onClick optional if it is
}

// Wrap the component with forwardRef
const MoreOptionsButton = forwardRef<HTMLButtonElement, MoreOptionsButtonProps>((
  { onClick, ...props }, 
  ref // Receive the ref
) => {
  const [isThemeSheetOpen, setIsThemeSheetOpen] = useState(false);
  const [showColorPickers, setShowColorPickers] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const { currentTheme, setTheme } = useTheme();
  const navigate = useNavigate();

  // Settings store
  const customColors = useSettingsStore((state) => state.appSettings.customColors);
  const updateCustomColors = useSettingsStore((state) => state.updateCustomColors);
  const resetCustomColors = useSettingsStore((state) => state.resetCustomColors);

  const navBgColor = useAutoOverlay(10, 'var(--bg-color)');

  const handleThemeChange = (theme: ThemeMode) => {
    setTheme(theme);
    setIsThemeSheetOpen(false);
  };

  const handleAnnotationSearch = () => {
    navigate('/annotations/search');
    setIsThemeSheetOpen(false);
  };

  const handleColorPickerToggle = () => {
    setShowColorPickers(!showColorPickers);
  };

  const handleTextColorChange = (color: string) => {
    updateCustomColors({
      ...customColors,
      textColor: color,
      enableCustomColors: true
    });
  };

  const handleBackgroundColorChange = (color: string) => {
    updateCustomColors({
      ...customColors,
      backgroundColor: color,
      enableCustomColors: true
    });
  };

  const handleResetColors = () => {
    resetCustomColors();
    setShowColorPickers(false);
  };

  const handleCustomColorsToggle = () => {
    updateCustomColors({
      ...customColors,
      enableCustomColors: !customColors?.enableCustomColors
    });
  };

  const handleClick = () => {
    setIsThemeSheetOpen(!isThemeSheetOpen);
    setShowColorPickers(false); // Reset color picker state when opening/closing
    if (onClick) onClick();
  };

  return (
    <>
      <button
        ref={ref || buttonRef} // Use passed ref or local ref
        onClick={handleClick}
        className="p-2 rounded-full transition-colors hover:bg-[var(--text-color)]/5"
        style={{ color: 'var(--text-color)' }}
        aria-label="Daha Fazla Seçenek"
        {...props} // Spread other props
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.2" strokeLinecap="round" strokeLinejoin="round">
          <circle cx="5" cy="12" r="1"></circle>
          <circle cx="12" cy="12" r="1"></circle>
          <circle cx="19" cy="12" r="1"></circle>
        </svg>
      </button>

      {/* Tema Seçim Sheet */}
      {isThemeSheetOpen && (
        <>
          {/* Arka Plan Overlay */}
          <div 
            className="fixed inset-0 bg-black/40 backdrop-blur-[1px] z-50 transition-opacity"
            onClick={() => setIsThemeSheetOpen(false)}
          />
          
          {/* Tema Seçim Paneli */}
          <div 
            className="fixed z-50 bottom-0 left-0 right-0 max-h-[85vh] md:top-[10%] md:left-1/2 md:transform md:-translate-x-1/2 md:w-[380px] md:max-h-[80vh] md:rounded-2xl rounded-t-2xl shadow-2xl border border-opacity-20 overflow-hidden flex flex-col"
            style={{
              backgroundColor: navBgColor,
              borderColor: 'color-mix(in srgb, var(--text-color) 15%, transparent)'
            }}
          >
            {/* Pull indicator for mobile */}
            <div className="md:hidden w-10 h-1 mx-auto mt-3 mb-1 rounded-full bg-[var(--text-color)] opacity-15 flex-shrink-0" />
            
            {/* Panel Header */}
            <div className="flex items-center justify-between p-3 border-b flex-shrink-0"
              style={{ borderColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)' }}
            >
              <h3 className="text-base font-medium" style={{ color: 'var(--text-color)' }}>Seçenekler</h3>
            </div>

            {/* Seçenekler */}
            <div className="p-4 space-y-2 overflow-y-auto flex-1">
              {/* Şerh Arama */}
              <button
                className="w-full text-left px-4 py-3 rounded-xl flex items-center gap-3 hover:bg-[var(--text-color)]/5 transition-colors"
                style={{ color: 'var(--text-color)' }}
                onClick={handleAnnotationSearch}
              >
                <Search size={20} />
                <span>Şerh Arama</span>
              </button>

              {/* Divider */}
              <div
                className="h-px mx-2 my-3"
                style={{ backgroundColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)' }}
              />

              {/* Tema Başlığı */}
              <div className="px-4 py-2">
                <h4 className="text-sm font-medium opacity-70" style={{ color: 'var(--text-color)' }}>
                  Tema Seçin
                </h4>
              </div>
              <button 
                className={`w-full text-left px-4 py-3 rounded-xl flex items-center gap-3 ${currentTheme.id === ThemeMode.LIGHT ? 'bg-[var(--text-color)]/10' : 'hover:bg-[var(--text-color)]/5'}`}
                style={{ color: 'var(--text-color)' }}
                onClick={() => handleThemeChange(ThemeMode.LIGHT)}
              >
                <Sun size={20} />
                <span>Aydınlık Tema</span>
              </button>
              
              <button 
                className={`w-full text-left px-4 py-3 rounded-xl flex items-center gap-3 ${currentTheme.id === ThemeMode.DARK ? 'bg-[var(--text-color)]/10' : 'hover:bg-[var(--text-color)]/5'}`}
                style={{ color: 'var(--text-color)' }}
                onClick={() => handleThemeChange(ThemeMode.DARK)}
              >
                <Moon size={20} />
                <span>Karanlık Tema</span>
              </button>
              
              <button 
                className={`w-full text-left px-4 py-3 rounded-xl flex items-center gap-3 ${currentTheme.id === ThemeMode.KREM ? 'bg-[var(--text-color)]/10' : 'hover:bg-[var(--text-color)]/5'}`}
                style={{ color: 'var(--text-color)' }}
                onClick={() => handleThemeChange(ThemeMode.KREM)}
              >
                <Coffee size={20} />
                <span>Krem Tema</span>
              </button>

              {/* Divider */}
              <div
                className="h-px mx-2 my-3"
                style={{ backgroundColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)' }}
              />

              {/* Özel Renkler Başlığı */}
              <div className="px-4 py-2">
                <h4 className="text-sm font-medium opacity-70" style={{ color: 'var(--text-color)' }}>
                  Özel Renkler
                </h4>
              </div>

              {/* Özel Renkleri Aktif/Pasif Yap */}
              <button
                className="w-full text-left px-4 py-3 rounded-xl flex items-center gap-3 hover:bg-[var(--text-color)]/5 transition-colors"
                style={{ color: 'var(--text-color)' }}
                onClick={handleCustomColorsToggle}
              >
                <div 
                  className={`w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${
                    customColors?.enableCustomColors ? 'bg-blue-500 border-blue-500' : 'border-gray-400'
                  }`}
                >
                  {customColors?.enableCustomColors && (
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
                <span>Özel Renkleri Kullan</span>
              </button>

              {/* Renk Seçicileri */}
              <button
                className={`w-full text-left px-4 py-3 rounded-xl flex items-center gap-3 transition-colors ${
                  showColorPickers ? 'bg-[var(--text-color)]/10' : 'hover:bg-[var(--text-color)]/5'
                }`}
                style={{ color: 'var(--text-color)' }}
                onClick={handleColorPickerToggle}
                disabled={!customColors?.enableCustomColors}
              >
                <Palette size={20} />
                <span>Renk Seçicileri</span>
                <svg 
                  className={`w-4 h-4 ml-auto transition-transform ${showColorPickers ? 'rotate-180' : ''}`}
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {/* Genişleyen Renk Seçici Bölümü */}
              {showColorPickers && customColors?.enableCustomColors && (
                <div className="px-4 pb-2 space-y-4">
                  {/* Text Color Picker */}
                  <RGBColorPicker
                    title="Metin Rengi"
                    selectedColor={customColors?.textColor}
                    defaultColor={currentTheme.colors.text}
                    onColorChange={handleTextColorChange}
                    compact
                  />

                  {/* Background Color Picker */}
                  <RGBColorPicker
                    title="Arkaplan Rengi"
                    selectedColor={customColors?.backgroundColor}
                    defaultColor={currentTheme.colors.background}
                    onColorChange={handleBackgroundColorChange}
                    compact
                  />

                  {/* Reset Button */}
                  <button
                    className="w-full px-4 py-2 rounded-xl flex items-center justify-center gap-2 bg-red-500/20 hover:bg-red-500/30 transition-colors"
                    style={{ color: 'var(--text-color)' }}
                    onClick={handleResetColors}
                  >
                    <RotateCcw size={16} />
                    <span>Renkleri Sıfırla</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </>
  );
});

MoreOptionsButton.displayName = 'MoreOptionsButton'; // Add display name for DevTools

export default MoreOptionsButton; 