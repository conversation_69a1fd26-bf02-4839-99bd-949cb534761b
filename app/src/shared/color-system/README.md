# 🎨 Tema-Aware Sabit Renk Sistemi

Modern, tema-aware renk sistemi. 8 gerçek renk (sarı, yeşil, mavi, mor, pembe, turuncu, kırmızı, teal) sağlar ve tema değişikliklerinde otomatik uyum sağlar.

## 📁 Klasör Yapısı

```
src/shared/color-system/
├── components/           # Renk sistemi component'leri
│   └── AdaptiveColorPicker.tsx
├── constants/           # Renk sabitleri ve konfigürasyonlar
│   └── colors.ts
├── hooks/              # Renk sistemi hook'ları
│   └── useColorSystem.ts
├── types/              # TypeScript tipleri
│   └── index.ts
├── utils/              # Yardımcı fonksiyonlar
│   └── colorUtils.ts
├── index.ts            # Ana export dosyası
└── README.md           # Bu dosya
```

## 🚀 Hızlı Başlangıç

### Temel Kullanım

```tsx
import { useColorSystem, AdaptiveColorPicker } from '@shared/color-system';

function MyComponent() {
  const { colors, getColor, currentTheme } = useColorSystem();
  
  return (
    <div style={{ color: getColor('blue', 'original') }}>
      Mevcut tema: {currentTheme}
    </div>
  );
}
```

### Color Picker Kullanımı (Annotation Sistemi)

```tsx
import { AdaptiveColorPicker } from '@shared/color-system';

function AnnotationColorPicker() {
  const [selectedColor, setSelectedColor] = useState('#fbbf24');
  const [selectedStyle, setSelectedStyle] = useState<'background' | 'text'>('background');

  const handleColorSelect = (color: string, style: 'background' | 'text') => {
    if (color === 'DELETE') {
      // Temizle işlemi
      handleClearAnnotations();
    } else {
      // Normal renk seçimi
      setSelectedColor(color);
      setSelectedStyle(style);
      onCreateHighlight(color, style);
    }
  };

  return (
    <AdaptiveColorPicker
      selectedColor={selectedColor}
      selectedStyle={selectedStyle}
      onColorSelect={handleColorSelect}
      compact={true}
      placement="below"
    />
  );
}
```

## 🎯 Ana Özellikler

### 1. Tema-Aware Renkler
- **Otomatik tema algılama**: Dark, Light, Krem
- **Dinamik parlaklık ayarı**: Her tema için optimize edilmiş
- **Gerçek zamanlı güncelleme**: Tema değişikliklerini otomatik izler

### 2. HSL Tabanlı Renk Sistemi
- **Kontrollü manipülasyon**: Hue, Saturation, Lightness ayrı ayrı
- **Öngörülebilir sonuçlar**: Matematik tabanlı renk hesaplamaları
- **Uyumlu renk paletleri**: Harmonik renk kombinasyonları

### 3. Erişilebilirlik
- **WCAG uyumluluğu**: AA ve AAA standartları
- **Kontrast oranı kontrolü**: Otomatik okunabilirlik garantisi
- **Renk körü dostu**: Erişilebilir renk seçimleri

### 4. Performance Optimizasyonu
- **Memoization**: Gereksiz hesaplamaları önler
- **Lazy loading**: İhtiyaç duyulduğunda hesaplama
- **Cache sistemi**: Tekrarlanan hesaplamaları önler

## 📚 API Referansı

### useColorSystem()

Ana renk sistemi hook'u. Tüm renk paletini ve utility fonksiyonları sağlar.

```tsx
const {
  colors,           // Tüm renk paleti
  highlightColors,  // Highlight için özel renkler
  currentTheme,     // Mevcut tema
  getColor,         // Renk alma fonksiyonu
  getContrastColor, // Kontrast rengi alma
  primaryColors,    // Primary renk isimleri
  semanticColors,   // Semantic renk isimleri
  accentColors      // Accent renk isimleri
} = useColorSystem();
```

### useColorPicker()

Color picker için optimize edilmiş hook.

```tsx
const {
  colors,    // Picker için optimize edilmiş renkler
  theme,     // Mevcut tema
  getColor   // Renk alma fonksiyonu
} = useColorPicker();
```

### useAdaptiveOverlay(intensity, options)

Tema-aware overlay renkleri için hook.

```tsx
const overlayColor = useAdaptiveOverlay(15, {
  baseColor: 'var(--bg-color)',
  mixColor: 'auto' // 'auto' | 'black' | 'white'
});
```

## 🎨 Renk Kategorileri

### Primary Colors
- **blue**: Ana mavi - linkler, butonlar
- **green**: Ana yeşil - başarı mesajları
- **yellow**: Ana sarı - uyarılar

### Semantic Colors
- **red**: Hata mesajları ve tehlike
- **orange**: Uyarı mesajları
- **purple**: Premium özellikler

### Accent Colors
- **pink**: Dekoratif ve vurgu
- **teal**: Sakinleştirici ve modern

## 🔧 Renk Varyantları

Her renk için 5 farklı varyant:

```tsx
colors.blue.original  // Ana renk
colors.blue.light     // Açık varyant
colors.blue.dark      // Koyu varyant
colors.blue.muted     // Soluk varyant
colors.blue.contrast  // Yüksek kontrast
```

## 🎯 Tema Bazında Optimizasyon

### Dark Theme
```tsx
// Koyu temada renkler daha açık
original: 65%  // Parlaklık
contrast: 85%  // Maksimum kontrast
```

### Light Theme
```tsx
// Açık temada renkler orta ton
original: 50%  // Parlaklık
contrast: 25%  // Maksimum kontrast
```

### Krem Theme
```tsx
// Krem temada renkler daha koyu
original: 45%  // Parlaklık
contrast: 20%  // Maksimum kontrast
```

## 🔄 Migration Guide

### Eski Sistemden Geçiş

```tsx
// ❌ ESKİ YÖNTEM
import { autoHue } from '@shared/hooks/autohue';
const style = autoHue('var(--text-color)');

// ✅ YENİ YÖNTEM
import { useEnhancedColorHue } from '@shared/color-system';
const enhancedColor = useEnhancedColorHue('var(--text-color)');
const style = { color: enhancedColor };
```

```tsx
// ❌ ESKİ YÖNTEM
import { useAutoOverlay } from '@shared/hooks/autooverlay';
const bgColor = useAutoOverlay(15, 'var(--bg-color)');

// ✅ YENİ YÖNTEM
import { useAdaptiveOverlay } from '@shared/color-system';
const bgColor = useAdaptiveOverlay(15);
```

## 🧪 Test ve Debug

### Color System Demo

Demo component'i kullanarak sistemi test edebilirsiniz:

```tsx
import ColorSystemDemo from '@shared/components/demo/ColorSystemDemo';

// Demo sayfasında kullanım
<ColorSystemDemo />
```

### Debug Utilities

```tsx
import { calculateContrastRatio, getColorAccessibility } from '@shared/color-system';

// Kontrast oranı kontrolü
const ratio = calculateContrastRatio('#ffffff', '#000000');

// Erişilebilirlik bilgisi
const accessibility = getColorAccessibility('#ffffff', '#000000');
console.log(accessibility.wcagLevel); // 'AAA'
```

## 🔮 Gelecek Planları

### v2.0 Özellikleri
- [ ] Gradient desteği
- [ ] Animation renkleri
- [ ] Daha fazla tema desteği
- [ ] Custom renk paleti oluşturma
- [ ] Renk blindness simülasyonu

### Performance İyileştirmeleri
- [ ] Web Workers ile renk hesaplaması
- [ ] IndexedDB cache sistemi
- [ ] Lazy loading optimizasyonu

## 🤝 Katkıda Bulunma

Yeni özellik önerileri ve bug raporları için issue açabilirsiniz.

### Geliştirme Kuralları
1. TypeScript tiplerini güncel tutun
2. Erişilebilirlik standartlarına uyun
3. Performance testlerini çalıştırın
4. Dokümantasyonu güncelleyin

## 📊 Sistem Karşılaştırması

| Özellik | Eski Sistem | Yeni Adaptive System |
|---------|-------------|---------------------|
| **Renk Tanımı** | Sabit hex değerler | HSL tabanlı dinamik |
| **Tema Uyumu** | ❌ Tema değişmez | ✅ Otomatik tema uyumu |
| **Erişilebilirlik** | ❌ Kontrast garantisi yok | ✅ WCAG AA/AAA uyumlu |
| **Yeniden Kullanım** | ❌ Zor ve dağınık | ✅ Merkezi ve kolay |
| **Bakım** | ❌ Çoklu dosya | ✅ Tek kaynak |
| **Performance** | ⚠️ Filter kullanımı | ✅ Optimize edilmiş |
| **Yan Etkiler** | ❌ CSS filter etkileri | ✅ Yan etkisiz |

## 🎉 Başarıyla Tamamlanan Geçiş

### ✅ Güncellenen Dosyalar
- `AnnotationFloatingPanel.tsx` → `useEnhancedColorHue`
- `AuthSheet.tsx` → `useEnhancedColorHue`
- `NavigationSheet.tsx` (Quran) → `useEnhancedColorHue`
- `NavigationSheet.tsx` (Risale) → `useEnhancedColorHue`
- `Mode3Content.tsx` → `useEnhancedColorHue`
- `TextActionMenu.tsx` → `AdaptiveColorPicker`

### 🗂️ Yeni Klasör Yapısı
```
src/shared/color-system/
├── components/AdaptiveColorPicker.tsx
├── constants/colors.ts
├── hooks/useColorSystem.ts
├── types/index.ts
├── utils/colorUtils.ts
├── index.ts
└── README.md
```

---

**🎨 Adaptive Color System v1.0** - Modern, tema-aware ve erişilebilir renk yönetimi artık hazır!
