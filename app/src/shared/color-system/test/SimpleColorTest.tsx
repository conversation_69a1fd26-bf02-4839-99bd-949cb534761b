/**
 * 🧪 Basit Color Picker Test
 * Text color bazlı dinamik renk sistemini test eder
 */

import React, { useState } from 'react';
import { useColorPicker } from '../hooks/useColorSystem';

export const SimpleColorTest: React.FC = () => {
  const { colors, theme } = useColorPicker();
  const [selectedColor, setSelectedColor] = useState<string>('');

  return (
    <div className="p-8 space-y-6" style={{ backgroundColor: 'var(--bg-color)', color: 'var(--text-color)' }}>
      
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-2">🎨 Dinamik Renk Sistemi Test</h1>
        <p className="text-sm opacity-70">
          Tema: <strong>{theme}</strong> | Text Color Bazlı 8 Dinamik Renk
        </p>
      </div>

      {/* Renk Paleti */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Dinamik Renk Paleti</h2>
        <div className="grid grid-cols-4 gap-4">
          {colors.map((color, index) => (
            <div 
              key={color.id}
              className="p-4 rounded-lg border cursor-pointer transition-all hover:scale-105"
              style={{ 
                backgroundColor: color.background,
                borderColor: color.value,
                color: color.textColor
              }}
              onClick={() => setSelectedColor(color.value)}
            >
              <div 
                className="w-8 h-8 rounded-full mx-auto mb-2 border"
                style={{ 
                  backgroundColor: color.value,
                  borderColor: color.textColor
                }}
              />
              <div className="text-xs font-bold text-center">{color.name}</div>
              <div className="text-xs text-center opacity-70">{color.id}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Seçili Renk Bilgileri */}
      {selectedColor && (
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Seçili Renk Detayları</h2>
          {colors.find(c => c.value === selectedColor) && (
            <div 
              className="p-4 rounded-lg border space-y-2"
              style={{ 
                backgroundColor: 'var(--bg-color)',
                borderColor: selectedColor,
                color: 'var(--text-color)'
              }}
            >
              {(() => {
                const color = colors.find(c => c.value === selectedColor)!;
                return (
                  <>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div><strong>İsim:</strong> {color.name}</div>
                      <div><strong>ID:</strong> {color.id}</div>
                      <div><strong>Renk:</strong> {color.value}</div>
                      <div><strong>Hue Shift:</strong> {color.hueShift}°</div>
                      <div><strong>Saturation Boost:</strong> +{color.saturationBoost}</div>
                      <div><strong>Lightness Adjust:</strong> {color.lightnessAdjust > 0 ? '+' : ''}{color.lightnessAdjust}</div>
                    </div>
                    
                    <div className="mt-4 space-y-2">
                      <div className="text-sm font-semibold">HSL Değerleri:</div>
                      <div className="grid grid-cols-2 gap-4 text-xs">
                        <div>
                          <strong>Orijinal:</strong><br/>
                          H: {Math.round(color.originalHsl.h)}°<br/>
                          S: {Math.round(color.originalHsl.s)}%<br/>
                          L: {Math.round(color.originalHsl.l)}%
                        </div>
                        <div>
                          <strong>Final:</strong><br/>
                          H: {Math.round(color.finalHsl.h)}°<br/>
                          S: {Math.round(color.finalHsl.s)}%<br/>
                          L: {Math.round(color.finalHsl.l)}%
                        </div>
                      </div>
                    </div>

                    <div className="mt-4 space-y-2">
                      <div className="text-sm font-semibold">Renk Varyantları:</div>
                      <div className="flex gap-2">
                        <div 
                          className="w-8 h-8 rounded border"
                          style={{ backgroundColor: color.value }}
                          title="Ana Renk"
                        />
                        <div 
                          className="w-8 h-8 rounded border"
                          style={{ backgroundColor: color.background }}
                          title="Background"
                        />
                        <div 
                          className="w-8 h-8 rounded border"
                          style={{ backgroundColor: color.hoverColor }}
                          title="Hover"
                        />
                        <div 
                          className="w-8 h-8 rounded border"
                          style={{ backgroundColor: color.muted }}
                          title="Muted"
                        />
                      </div>
                    </div>

                    <div 
                      className="mt-4 p-3 rounded text-center"
                      style={{ 
                        backgroundColor: selectedColor,
                        color: color.textColor
                      }}
                    >
                      Bu renk ile örnek metin
                    </div>
                  </>
                );
              })()}
            </div>
          )}
        </div>
      )}

      {/* Kullanım Bilgileri */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Kullanım Bilgileri</h2>
        <div 
          className="p-4 rounded-lg border text-sm space-y-2"
          style={{ 
            backgroundColor: 'var(--bg-color)',
            borderColor: 'var(--text-color)',
            color: 'var(--text-color)'
          }}
        >
          <p><strong>✅ Text Color Bazlı:</strong> Renkler mevcut text color'ın HSL değerlerine göre oluşturuluyor</p>
          <p><strong>✅ 8 Dinamik Renk:</strong> 45° aralıklarla hue shift uygulanıyor</p>
          <p><strong>✅ Tema Aware:</strong> Dark/Light tema değişikliklerinde otomatik uyum</p>
          <p><strong>✅ Saturation Boost:</strong> Text color'ın saturation'ına göre dinamik artış</p>
          <p><strong>✅ Lightness Adjust:</strong> Tema bazında parlaklık optimizasyonu</p>
          
          <div className="mt-4 p-2 rounded" style={{ backgroundColor: 'var(--bg-secondary)' }}>
            <strong>Picker'da Kullanım:</strong><br/>
            <code className="text-xs">
              {`import { useColorPicker } from '@shared/color-system';
const { colors } = useColorPicker();
// colors[0].id = 'color_1', colors[0].name = 'Ana Renk'`}
            </code>
          </div>
        </div>
      </div>

    </div>
  );
};

export default SimpleColorTest;
