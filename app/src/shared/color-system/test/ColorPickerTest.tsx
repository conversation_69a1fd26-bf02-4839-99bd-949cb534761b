/**
 * 🧪 Color Picker Test Sayfası
 * Text color bazlı dinamik renk sistemini test eder
 */

import React, { useState } from 'react';
import { useColorPicker } from '../hooks/useColorSystem';
import { AdaptiveColorPicker } from '../components/AdaptiveColorPicker';

export const ColorPickerTest: React.FC = () => {
  const { colors, theme } = useColorPicker();
  const [selectedColor, setSelectedColor] = useState('#fbbf24');
  const [selectedStyle, setSelectedStyle] = useState<'background' | 'text'>('background');
  const [showPicker, setShowPicker] = useState(true);

  const handleColorSelect = (color: string, style: 'background' | 'text') => {
    if (color === 'DELETE') {
      setSelectedColor('');
      console.log('🗑️ Renk temizlendi');
    } else {
      setSelectedColor(color);
      setSelectedStyle(style);
      console.log('🎨 Renk seçildi:', { color, style });
    }
  };

  return (
    <div className="min-h-screen p-8" style={{ backgroundColor: 'var(--bg-color)' }}>
      <div className="max-w-4xl mx-auto space-y-8">
        
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold" style={{ color: 'var(--text-color)' }}>
            🧪 Color Picker Test
          </h1>
          <p className="text-lg" style={{ color: 'var(--text-color)' }}>
            Text Color Bazlı Dinamik Renk Sistemi
          </p>
          <div className="text-sm opacity-70" style={{ color: 'var(--text-color)' }}>
            Tema: <strong>{theme}</strong> | 
            Text Color: <span style={{ 
              color: 'var(--text-color)', 
              backgroundColor: 'var(--text-color)', 
              padding: '2px 6px', 
              borderRadius: '4px',
              color: 'var(--bg-color)'
            }}>
              var(--text-color)
            </span>
          </div>
        </div>

        {/* Dinamik Renk Paleti Gösterimi */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold" style={{ color: 'var(--text-color)' }}>
            📊 Dinamik Renk Paleti (8 Renk)
          </h2>
          <div className="grid grid-cols-4 gap-4">
            {colors.map((color, index) => (
              <div 
                key={index}
                className="p-4 rounded-lg border-2 text-center transition-all hover:scale-105"
                style={{ 
                  backgroundColor: color.background,
                  borderColor: color.value,
                  color: color.textColor
                }}
              >
                <div 
                  className="w-12 h-12 rounded-full mx-auto mb-3 border-2 shadow-lg"
                  style={{ 
                    backgroundColor: color.value,
                    borderColor: color.textColor
                  }}
                />
                <div className="font-bold text-sm">{color.name}</div>
                <div className="text-xs opacity-80 font-mono mt-1">{color.value}</div>
                <div className="text-xs opacity-60 mt-1">
                  BG: {color.background}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Color Picker Test */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold" style={{ color: 'var(--text-color)' }}>
              🎨 AdaptiveColorPicker Test
            </h2>
            <button
              onClick={() => setShowPicker(!showPicker)}
              className="px-4 py-2 rounded-lg border transition-colors"
              style={{
                backgroundColor: 'var(--bg-color)',
                borderColor: 'var(--text-color)',
                color: 'var(--text-color)'
              }}
            >
              {showPicker ? 'Picker\'ı Gizle' : 'Picker\'ı Göster'}
            </button>
          </div>

          <div className="flex gap-8">
            {/* Color Picker */}
            <div className="relative">
              {showPicker && (
                <AdaptiveColorPicker
                  selectedColor={selectedColor}
                  selectedStyle={selectedStyle}
                  onColorSelect={handleColorSelect}
                  compact={false}
                  placement="below"
                />
              )}
            </div>

            {/* Seçim Bilgileri */}
            <div className="flex-1 space-y-4">
              <div 
                className="p-4 rounded-lg border"
                style={{
                  backgroundColor: 'var(--bg-color)',
                  borderColor: 'var(--text-color)',
                  color: 'var(--text-color)'
                }}
              >
                <h3 className="font-semibold mb-2">Seçim Bilgileri:</h3>
                <div className="space-y-1 text-sm">
                  <div><strong>Renk:</strong> {selectedColor || 'Seçilmedi'}</div>
                  <div><strong>Stil:</strong> {selectedStyle}</div>
                  <div><strong>Tema:</strong> {theme}</div>
                </div>
              </div>

              {/* Önizleme */}
              {selectedColor && (
                <div 
                  className="p-6 rounded-lg border-2 text-center"
                  style={{
                    backgroundColor: selectedStyle === 'background' ? selectedColor : 'var(--bg-color)',
                    color: selectedStyle === 'text' ? selectedColor : 'var(--text-color)',
                    borderColor: selectedColor
                  }}
                >
                  <h3 className="text-lg font-bold mb-2">Önizleme</h3>
                  <p>Bu metin seçilen renk ile {selectedStyle === 'background' ? 'arka plan' : 'metin'} rengi olarak görüntüleniyor.</p>
                  <div className="mt-4 text-sm opacity-80">
                    Renk: {selectedColor} | Stil: {selectedStyle}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Test Talimatları */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold" style={{ color: 'var(--text-color)' }}>
            📋 Test Talimatları
          </h2>
          <div 
            className="p-6 rounded-lg border space-y-3"
            style={{
              backgroundColor: 'var(--bg-color)',
              borderColor: 'var(--text-color)',
              color: 'var(--text-color)'
            }}
          >
            <div className="font-semibold">Test Adımları:</div>
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li><strong>Tema Değiştir:</strong> Navbar'dan tema değiştir ve renklerin otomatik güncellendiğini gör</li>
              <li><strong>Özel Renk Seç:</strong> MoreOptionsButton'dan özel text color seç ve paletteki değişimi izle</li>
              <li><strong>Color Picker Test:</strong> Picker'dan renk seç ve önizlemeyi kontrol et</li>
              <li><strong>Stil Değiştir:</strong> Text/Background stillerini test et</li>
              <li><strong>Temizle:</strong> DELETE butonunu test et</li>
            </ol>
            
            <div className="mt-4 p-3 rounded border-l-4" style={{ borderColor: selectedColor || 'var(--text-color)' }}>
              <div className="font-semibold text-sm">💡 Beklenen Davranış:</div>
              <ul className="list-disc list-inside text-xs mt-1 space-y-1">
                <li>Her tema değişikliğinde 8 renk otomatik güncellenmeli</li>
                <li>Text color değiştiğinde renkler yeniden hesaplanmalı</li>
                <li>Renkler text color'ın HSL değerlerine göre 45° aralıklarla oluşmalı</li>
                <li>Dark temada renkler daha açık, light temada daha koyu olmalı</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Debug Bilgileri */}
        <details className="space-y-2">
          <summary 
            className="cursor-pointer font-semibold p-2 rounded border"
            style={{ 
              backgroundColor: 'var(--bg-color)',
              borderColor: 'var(--text-color)',
              color: 'var(--text-color)'
            }}
          >
            🔍 Debug Bilgileri (Tıkla)
          </summary>
          <div 
            className="p-4 rounded border text-xs font-mono space-y-2"
            style={{
              backgroundColor: 'var(--bg-color)',
              borderColor: 'var(--text-color)',
              color: 'var(--text-color)'
            }}
          >
            <div><strong>Tema:</strong> {theme}</div>
            <div><strong>Renk Sayısı:</strong> {colors.length}</div>
            <div><strong>Seçili Renk:</strong> {selectedColor}</div>
            <div><strong>Seçili Stil:</strong> {selectedStyle}</div>
            <div className="mt-3"><strong>Renk Detayları:</strong></div>
            {colors.map((color, index) => (
              <div key={index} className="ml-2">
                {index + 1}. {color.name}: {color.value}
              </div>
            ))}
          </div>
        </details>

      </div>
    </div>
  );
};

export default ColorPickerTest;
