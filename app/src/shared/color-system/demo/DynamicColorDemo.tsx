/**
 * 🎨 Dinamik Renk Sistemi Demo
 * Tema değişikliklerinde renklerin nasıl otomatik uyum sağladığını gösterir
 */

import React, { useState } from 'react';
import { useColorPicker, useColorSystem } from '../hooks/useColorSystem';
import { AdaptiveColorPicker } from '../components/AdaptiveColorPicker';
import { optimizeColorForTheme, optimizeColorForAnnotation } from '../utils/colorUtils';

export const DynamicColorDemo: React.FC = () => {
  const { colors, theme } = useColorPicker();
  const { currentTheme } = useColorSystem();
  const [selectedColor, setSelectedColor] = useState('#fbbf24');
  const [selectedStyle, setSelectedStyle] = useState<'background' | 'text'>('background');

  const handleColorSelect = (color: string, style: 'background' | 'text') => {
    if (color === 'DELETE') {
      setSelectedColor('');
    } else {
      setSelectedColor(color);
      setSelectedStyle(style);
    }
  };

  return (
    <div className="p-8 space-y-8">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-2" style={{ color: 'var(--text-color)' }}>
          🎨 Text Color Bazlı Dinamik Renk Sistemi
        </h1>
        <p className="text-sm opacity-70" style={{ color: 'var(--text-color)' }}>
          Mevcut tema: <strong>{currentTheme}</strong> | Text Color: <strong style={{ color: 'var(--text-color)' }}>var(--text-color)</strong>
        </p>
        <p className="text-xs opacity-50 mt-1" style={{ color: 'var(--text-color)' }}>
          Tema değiştirin veya özel renk seçin - renkler otomatik güncellenecek!
        </p>
      </div>

      {/* Text Color Bazlı Dinamik Renk Paleti */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold" style={{ color: 'var(--text-color)' }}>
          Text Color Bazlı 8 Dinamik Renk (AutoHue)
        </h2>
        <p className="text-sm opacity-70" style={{ color: 'var(--text-color)' }}>
          Bu renkler mevcut text color'ın HSL değerlerine göre 45° aralıklarla oluşturuluyor
        </p>
        <div className="grid grid-cols-4 gap-4">
          {colors.map((color, index) => (
            <div 
              key={index}
              className="p-4 rounded-lg border text-center"
              style={{ 
                backgroundColor: color.background,
                borderColor: 'var(--border-color)',
                color: color.textColor
              }}
            >
              <div 
                className="w-8 h-8 rounded-full mx-auto mb-2 border"
                style={{ 
                  backgroundColor: color.value,
                  borderColor: color.textColor
                }}
              />
              <div className="text-sm font-medium">{color.name}</div>
              <div className="text-xs opacity-70 font-mono">{color.value}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Color Picker Demo */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold" style={{ color: 'var(--text-color)' }}>
          AdaptiveColorPicker Demo
        </h2>
        <div className="flex items-center gap-4">
          <div className="relative">
            <AdaptiveColorPicker
              selectedColor={selectedColor}
              selectedStyle={selectedStyle}
              onColorSelect={handleColorSelect}
              compact={true}
              placement="below"
            />
          </div>
          <div className="flex-1 space-y-2">
            <div>
              <strong>Seçili Renk:</strong> {selectedColor || 'Hiçbiri'}
            </div>
            <div>
              <strong>Stil:</strong> {selectedStyle}
            </div>
            {selectedColor && (
              <div 
                className="p-3 rounded border"
                style={{
                  backgroundColor: selectedStyle === 'background' ? selectedColor : 'transparent',
                  color: selectedStyle === 'text' ? selectedColor : 'var(--text-color)',
                  borderColor: 'var(--border-color)'
                }}
              >
                Örnek metin - {selectedStyle === 'background' ? 'arka plan' : 'metin'} rengi
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Tema Optimizasyonu Demo */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold" style={{ color: 'var(--text-color)' }}>
          Tema Optimizasyonu Demo
        </h2>
        <div className="grid grid-cols-3 gap-4">
          {['dark', 'light', 'krem'].map((demoTheme) => (
            <div 
              key={demoTheme}
              className="p-4 rounded-lg border space-y-2"
              style={{ 
                backgroundColor: 'var(--bg-color)',
                borderColor: 'var(--border-color)'
              }}
            >
              <h3 className="font-medium" style={{ color: 'var(--text-color)' }}>
                {demoTheme.charAt(0).toUpperCase() + demoTheme.slice(1)} Tema
              </h3>
              {colors.slice(0, 4).map((color, index) => {
                const optimized = optimizeColorForTheme(color.value, demoTheme as any);
                return (
                  <div key={index} className="flex items-center gap-2">
                    <div 
                      className="w-4 h-4 rounded border"
                      style={{ 
                        backgroundColor: optimized,
                        borderColor: 'var(--border-color)'
                      }}
                    />
                    <span className="text-xs font-mono" style={{ color: 'var(--text-color)' }}>
                      {optimized}
                    </span>
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </div>

      {/* Annotation Optimizasyonu Demo */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold" style={{ color: 'var(--text-color)' }}>
          Annotation Optimizasyonu Demo
        </h2>
        <div className="grid grid-cols-3 gap-4">
          {['note', 'highlight', 'bookmark'].map((annotationType) => (
            <div 
              key={annotationType}
              className="p-4 rounded-lg border space-y-2"
              style={{ 
                backgroundColor: 'var(--bg-color)',
                borderColor: 'var(--border-color)'
              }}
            >
              <h3 className="font-medium" style={{ color: 'var(--text-color)' }}>
                {annotationType.charAt(0).toUpperCase() + annotationType.slice(1)}
              </h3>
              {colors.slice(0, 3).map((color, index) => {
                const optimized = optimizeColorForAnnotation(
                  color.value, 
                  currentTheme, 
                  annotationType as any
                );
                return (
                  <div key={index} className="flex items-center gap-2">
                    <div 
                      className="w-4 h-4 rounded border"
                      style={{ 
                        backgroundColor: optimized,
                        borderColor: 'var(--border-color)'
                      }}
                    />
                    <span className="text-xs font-mono" style={{ color: 'var(--text-color)' }}>
                      {optimized}
                    </span>
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </div>

      {/* Kullanım Talimatları */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold" style={{ color: 'var(--text-color)' }}>
          Kullanım Talimatları
        </h2>
        <div 
          className="p-4 rounded-lg border space-y-2 text-sm"
          style={{ 
            backgroundColor: 'var(--bg-color)',
            borderColor: 'var(--border-color)',
            color: 'var(--text-color)'
          }}
        >
          <p><strong>1.</strong> Temayı değiştirin ve renklerin otomatik uyum sağladığını görün</p>
          <p><strong>2.</strong> Color picker'dan renk seçin ve önizlemeyi kontrol edin</p>
          <p><strong>3.</strong> Tema optimizasyonu ile renklerin nasıl değiştiğini inceleyin</p>
          <p><strong>4.</strong> Annotation tipine göre renk optimizasyonunu test edin</p>
        </div>
      </div>
    </div>
  );
};

export default DynamicColorDemo;
