import { useState, useEffect, useMemo } from 'react';
import type {
  Theme,
  ColorVariants,
  HighlightColor,
  UseColorSystemReturn,
  UseColorPickerReturn,
  UseAdaptiveOverlayOptions,
  PickerColor
} from '../types';
import { COLOR_DEFINITIONS, THEME_LIGHTNESS_MAP } from '../constants/colors';
import { hslToString, getCurrentTheme, generateDynamicColorPalette } from '../utils/colorUtils';

/**
 * 🎨 Advanced Color System Hook
 * Tema-aware, erişilebilir ve yeniden kullanılabilir renk sistemi
 */

/**
 * Tema bazında parlaklık değerlerini hesaplar
 */
const getThemeBasedLightness = (theme: Theme, variant: keyof ColorVariants): number => {
  return THEME_LIGHTNESS_MAP[theme][variant];
};

/**
 * Ana renk sistemi hook'u
 */
export const useColorSystem = (): UseColorSystemReturn => {
  const [currentTheme, setCurrentTheme] = useState<Theme>(getCurrentTheme);

  // Tema değişikliklerini izle
  useEffect(() => {
    const updateTheme = () => {
      setCurrentTheme(getCurrentTheme());
    };

    const observer = new MutationObserver(updateTheme);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['style']
    });

    return () => observer.disconnect();
  }, []);

  // Renk paletini oluştur
  const colorPalette = useMemo(() => {
    const palette: Record<string, ColorVariants> = {};

    COLOR_DEFINITIONS.forEach(({ hue, saturation, name }) => {
      palette[name] = {
        original: hslToString(hue, saturation, getThemeBasedLightness(currentTheme, 'original')),
        light: hslToString(hue, saturation * 0.7, getThemeBasedLightness(currentTheme, 'light')),
        dark: hslToString(hue, saturation, getThemeBasedLightness(currentTheme, 'dark')),
        muted: hslToString(hue, saturation * 0.5, getThemeBasedLightness(currentTheme, 'muted')),
        contrast: hslToString(hue, saturation, getThemeBasedLightness(currentTheme, 'contrast'))
      };
    });

    return palette;
  }, [currentTheme]);

  // Highlight için özel renk paleti
  const highlightColors = useMemo((): HighlightColor[] => {
    return COLOR_DEFINITIONS.map(({ name }) => ({
      name: name.charAt(0).toUpperCase() + name.slice(1),
      value: colorPalette[name].original,
      background: colorPalette[name].light,
      muted: colorPalette[name].muted
    }));
  }, [colorPalette]);

  // Utility fonksiyonlar
  const getColor = (colorName: string, variant: keyof ColorVariants = 'original'): string => {
    return colorPalette[colorName]?.[variant] || colorPalette.blue?.original || '#3b82f6';
  };

  const getContrastColor = (colorName: string): string => {
    return colorPalette[colorName]?.contrast || colorPalette.blue?.contrast || '#1e40af';
  };

  // Renk kategorileri
  const primaryColors = COLOR_DEFINITIONS.filter(c => c.category === 'primary').map(c => c.name);
  const semanticColors = COLOR_DEFINITIONS.filter(c => c.category === 'semantic').map(c => c.name);
  const accentColors = COLOR_DEFINITIONS.filter(c => c.category === 'accent').map(c => c.name);

  return {
    colors: colorPalette,
    highlightColors,
    currentTheme,
    getColor,
    getContrastColor,
    primaryColors,
    semanticColors,
    accentColors,
  };
};

/**
 * Renk picker için özel hook - Text color bazlı dinamik renk sistemi
 */
export const useColorPicker = (): UseColorPickerReturn => {
  const { currentTheme } = useColorSystem();
  const [textColor, setTextColor] = useState<string>('var(--text-color)');

  // Text color'ı gerçek zamanlı olarak izle
  useEffect(() => {
    const updateTextColor = () => {
      const root = document.documentElement;
      const currentTextColor = getComputedStyle(root).getPropertyValue('--text-color').trim();
      if (currentTextColor) {
        setTextColor(currentTextColor);
      }
    };

    // İlk yükleme
    updateTextColor();

    // CSS değişkenlerini izle
    const observer = new MutationObserver(updateTextColor);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['style']
    });

    // Tema değişikliklerini de izle
    const themeObserver = new MutationObserver(updateTextColor);
    themeObserver.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class', 'data-theme']
    });

    return () => {
      observer.disconnect();
      themeObserver.disconnect();
    };
  }, []);

  // Text color bazlı dinamik renk paleti - text color değiştiğinde otomatik güncellenir
  const colors = useMemo((): PickerColor[] => {
    return generateDynamicColorPalette(textColor);
  }, [textColor]);

  // Utility fonksiyonlar
  const getColor = (colorName: string, variant: keyof ColorVariants = 'original'): string => {
    const color = colors.find(c => c.name.toLowerCase() === colorName.toLowerCase());
    return color?.value || colors[0]?.value || '#3b82f6';
  };

  return {
    colors,
    theme: currentTheme,
    getColor
  };
};

/**
 * Tema-aware overlay hook'u (autoOverlay'in gelişmiş versiyonu)
 */
export const useAdaptiveOverlay = (
  intensity: number = 10, 
  options: UseAdaptiveOverlayOptions = {}
): string => {
  const { currentTheme } = useColorSystem();
  const [overlayColor, setOverlayColor] = useState<string>('');

  const {
    baseColor = 'var(--bg-color)',
    mixColor = 'auto'
  } = options;

  useEffect(() => {
    const updateOverlay = () => {
      let finalMixColor = mixColor;
      
      if (mixColor === 'auto') {
        finalMixColor = currentTheme === 'light' ? 'black' : 'white';
      }
      
      setOverlayColor(`color-mix(in srgb, ${baseColor} ${100 - intensity}%, ${finalMixColor})`);
    };

    updateOverlay();

    const observer = new MutationObserver(updateOverlay);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['style']
    });

    return () => observer.disconnect();
  }, [intensity, currentTheme, baseColor, mixColor]);

  return overlayColor;
};

/**
 * Enhanced hue hook - autoHue'nun gelişmiş versiyonu
 */
export const useEnhancedColorHue = (baseColor: string): string => {
  const { currentTheme } = useColorSystem();
  const [enhancedColor, setEnhancedColor] = useState<string>('');

  useEffect(() => {
    const updateColor = () => {
      // Basit implementasyon - daha gelişmiş hale getirilebilir
      const isLight = currentTheme === 'light';
      const hueShift = isLight ? 30 : 45;
      const saturationBoost = isLight ? 15 : 25;
      const lightnessAdjust = isLight ? -10 : 15;

      // Bu implementasyon daha da geliştirilebilir
      setEnhancedColor(`color-mix(in srgb, ${baseColor} 70%, ${isLight ? '#2563eb' : '#60a5fa'})`);
    };

    updateColor();

    const observer = new MutationObserver(updateColor);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['style']
    });

    return () => observer.disconnect();
  }, [baseColor, currentTheme]);

  return enhancedColor;
};
