/**
 * 🎨 Color System Types
 * Renk sistemi için tüm TypeScript tipleri
 */

// Tema tipi
export type Theme = 'dark' | 'light' | 'krem';

// Renk kategorileri
export type ColorCategory = 'primary' | 'semantic' | 'accent';

// Renk varyantları
export type ColorVariant = 'original' | 'light' | 'dark' | 'muted' | 'contrast';

// Temel renk tanımı (HSL formatında)
export interface ColorDefinition {
  hue: number;        // 0-360
  saturation: number; // 0-100
  name: string;
  category: ColorCategory;
  description?: string;
}

// Renk varyantları objesi
export interface ColorVariants {
  original: string;
  light: string;
  dark: string;
  muted: string;
  contrast: string; // Yüksek kontrast versiyonu
}

// Highlight için özel renk tipi
export interface HighlightColor {
  name: string;
  value: string;
  background: string;
  muted: string;
  textColor?: string;
  hoverColor?: string;
}

// Color picker için renk tipi - Detaylı bilgilerle
export interface PickerColor {
  name: string;
  id: string;
  value: string;
  background: string;
  textColor: string;
  hoverColor: string;
  muted: string;
  hueShift: number;
  saturationBoost: number;
  lightnessAdjust: number;
  originalHsl: { h: number; s: number; l: number };
  finalHsl: { h: number; s: number; l: number };
}

// Tema bazında parlaklık haritası
export type ThemeLightnessMap = {
  [key in Theme]: {
    [key in keyof ColorVariants]: number;
  };
};

// Color system hook return tipi
export interface UseColorSystemReturn {
  // Ana renk paleti
  colors: Record<string, ColorVariants>;
  
  // Highlight renkleri
  highlightColors: HighlightColor[];
  
  // Mevcut tema
  currentTheme: Theme;
  
  // Utility fonksiyonlar
  getColor: (colorName: string, variant?: ColorVariant) => string;
  getContrastColor: (colorName: string) => string;
  
  // Renk grupları
  primaryColors: string[];
  semanticColors: string[];
  accentColors: string[];
}

// Color picker hook return tipi
export interface UseColorPickerReturn {
  colors: PickerColor[];
  theme: Theme;
  getColor: (colorName: string, variant?: ColorVariant) => string;
}

// Adaptive overlay hook parametreleri
export interface UseAdaptiveOverlayOptions {
  intensity?: number;
  baseColor?: string;
  mixColor?: 'auto' | 'black' | 'white';
}

// Renk accessibility bilgileri
export interface ColorAccessibility {
  contrastRatio: number;
  wcagLevel: 'AA' | 'AAA' | 'FAIL';
  isReadable: boolean;
}

// Renk geçiş animasyonu tipi
export interface ColorTransition {
  from: string;
  to: string;
  duration: number;
  easing?: string;
}

// Renk paleti konfigürasyonu
export interface ColorPaletteConfig {
  includeCategories?: ColorCategory[];
  excludeColors?: string[];
  customColors?: ColorDefinition[];
  generateVariants?: boolean;
}
