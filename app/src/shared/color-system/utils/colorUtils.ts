import type { Theme, ColorAccessibility } from '../types';
import { CSS_VARIABLES, ACCESSIBILITY_STANDARDS } from '../constants/colors';

/**
 * 🎨 Color System Utilities
 * Renk sistemi için yardımcı fonksiyonlar
 */

/**
 * RGB'den HSL'e dönüştürme fonksiyonu
 */
export const rgbToHsl = (r: number, g: number, b: number): [number, number, number] => {
  r /= 255;
  g /= 255;
  b /= 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  return [h * 360, s * 100, l * 100];
};

/**
 * HSL'den RGB'ye dönüştürme fonksiyonu
 */
export const hslToRgb = (h: number, s: number, l: number): [number, number, number] => {
  h /= 360;
  s /= 100;
  l /= 100;

  const hue2rgb = (p: number, q: number, t: number): number => {
    if (t < 0) t += 1;
    if (t > 1) t -= 1;
    if (t < 1/6) return p + (q - p) * 6 * t;
    if (t < 1/2) return q;
    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
    return p;
  };

  if (s === 0) {
    return [l * 255, l * 255, l * 255]; // achromatic
  }

  const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
  const p = 2 * l - q;
  const r = hue2rgb(p, q, h + 1/3);
  const g = hue2rgb(p, q, h);
  const b = hue2rgb(p, q, h - 1/3);

  return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)];
};

/**
 * HSL'den CSS renk stringine çevirir
 */
export const hslToString = (h: number, s: number, l: number): string => {
  return `hsl(${Math.round(h)}, ${Math.round(s)}%, ${Math.round(l)}%)`;
};

/**
 * RGB'den CSS renk stringine çevirir
 */
export const rgbToString = (r: number, g: number, b: number): string => {
  return `rgb(${Math.round(r)}, ${Math.round(g)}, ${Math.round(b)})`;
};

/**
 * CSS renk değerini RGB değerlerine çevirir
 */
export const parseColor = (color: string): [number, number, number] | null => {
  try {
    // CSS variable ise değerini al
    if (color.startsWith('var(')) {
      const root = document.documentElement;
      const varName = color.replace('var(', '').replace(')', '');
      color = getComputedStyle(root).getPropertyValue(varName).trim();
    }

    // Hex format
    if (color.startsWith('#')) {
      const hex = color.slice(1);
      if (hex.length === 3) {
        return [
          parseInt(hex[0] + hex[0], 16),
          parseInt(hex[1] + hex[1], 16),
          parseInt(hex[2] + hex[2], 16)
        ];
      } else if (hex.length === 6) {
        return [
          parseInt(hex.slice(0, 2), 16),
          parseInt(hex.slice(2, 4), 16),
          parseInt(hex.slice(4, 6), 16)
        ];
      }
    }

    // RGB format
    const rgbMatch = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
    if (rgbMatch) {
      return [
        parseInt(rgbMatch[1]),
        parseInt(rgbMatch[2]),
        parseInt(rgbMatch[3])
      ];
    }

    // HSL format
    const hslMatch = color.match(/hsl\((\d+),\s*(\d+)%,\s*(\d+)%\)/);
    if (hslMatch) {
      const [r, g, b] = hslToRgb(
        parseInt(hslMatch[1]),
        parseInt(hslMatch[2]),
        parseInt(hslMatch[3])
      );
      return [r, g, b];
    }

    return null;
  } catch (error) {
    console.error('Color parsing error:', error);
    return null;
  }
};

/**
 * Rengin parlaklığını kontrol eder (YIQ formülü)
 */
export const isLightColor = (r: number, g: number, b: number): boolean => {
  const yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;
  return yiq >= 128;
};

/**
 * CSS renk stringi için parlaklık kontrolü
 */
export const isLightColorString = (color: string): boolean => {
  const rgb = parseColor(color);
  if (!rgb) return true; // Varsayılan olarak açık kabul et
  
  const [r, g, b] = rgb;
  return isLightColor(r, g, b);
};

/**
 * İki renk arasındaki kontrast oranını hesaplar
 */
export const calculateContrastRatio = (color1: string, color2: string): number => {
  const rgb1 = parseColor(color1);
  const rgb2 = parseColor(color2);
  
  if (!rgb1 || !rgb2) return 1;

  const getLuminance = (r: number, g: number, b: number): number => {
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  };

  const lum1 = getLuminance(...rgb1);
  const lum2 = getLuminance(...rgb2);
  
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  
  return (brightest + 0.05) / (darkest + 0.05);
};

/**
 * Renk erişilebilirlik bilgilerini döndürür
 */
export const getColorAccessibility = (foreground: string, background: string): ColorAccessibility => {
  const contrastRatio = calculateContrastRatio(foreground, background);
  
  let wcagLevel: 'AA' | 'AAA' | 'FAIL' = 'FAIL';
  if (contrastRatio >= ACCESSIBILITY_STANDARDS.WCAG_AAA_NORMAL) {
    wcagLevel = 'AAA';
  } else if (contrastRatio >= ACCESSIBILITY_STANDARDS.WCAG_AA_NORMAL) {
    wcagLevel = 'AA';
  }

  return {
    contrastRatio,
    wcagLevel,
    isReadable: contrastRatio >= ACCESSIBILITY_STANDARDS.WCAG_AA_NORMAL
  };
};

/**
 * Mevcut temayı algılar
 */
export const getCurrentTheme = (): Theme => {
  const root = document.documentElement;
  const bgColor = getComputedStyle(root).getPropertyValue(CSS_VARIABLES.BG_COLOR).trim();
  
  // Tema algılama
  if (bgColor === '#121212') return 'dark';
  if (bgColor === '#F7F2E2') return 'krem';
  return 'light';
};

/**
 * Rengi belirtilen miktarda koyulaştırır
 */
export const darkenColor = (color: string, amount: number): string => {
  const rgb = parseColor(color);
  if (!rgb) return color;

  const [h, s, l] = rgbToHsl(...rgb);
  const newL = Math.max(0, l - amount);
  
  return hslToString(h, s, newL);
};

/**
 * Rengi belirtilen miktarda açar
 */
export const lightenColor = (color: string, amount: number): string => {
  const rgb = parseColor(color);
  if (!rgb) return color;

  const [h, s, l] = rgbToHsl(...rgb);
  const newL = Math.min(100, l + amount);
  
  return hslToString(h, s, newL);
};

/**
 * Rengin canlılığını ayarlar
 */
export const adjustSaturation = (color: string, amount: number): string => {
  const rgb = parseColor(color);
  if (!rgb) return color;

  const [h, s, l] = rgbToHsl(...rgb);
  const newS = Math.max(0, Math.min(100, s + amount));
  
  return hslToString(h, newS, l);
};

/**
 * Rengin tonunu değiştirir
 */
export const adjustHue = (color: string, amount: number): string => {
  const rgb = parseColor(color);
  if (!rgb) return color;

  const [h, s, l] = rgbToHsl(...rgb);
  const newH = (h + amount) % 360;

  return hslToString(newH, s, l);
};

/**
 * 🎨 Dinamik Tema-Aware Renk Sistemi
 * autohue.ts'den esinlenen gelişmiş renk manipülasyonu
 */

/**
 * Rengi tema bazında optimize eder
 */
export const optimizeColorForTheme = (baseColor: string, theme: Theme): string => {
  const rgb = parseColor(baseColor);
  if (!rgb) return baseColor;

  const [h, s, l] = rgbToHsl(...rgb);
  const isLight = isLightColor(...rgb);

  let newHue = h;
  let newSaturation = s;
  let newLightness = l;

  // Tema bazında optimizasyon
  switch (theme) {
    case 'dark':
      // Koyu temada daha açık ve canlı renkler
      newSaturation = Math.min(s + 15, 85);
      newLightness = isLight ? Math.max(l - 10, 35) : Math.min(l + 20, 75);
      break;

    case 'light':
      // Açık temada daha koyu ve yumuşak renkler
      newSaturation = Math.max(s - 10, 40);
      newLightness = isLight ? Math.max(l - 20, 25) : Math.min(l + 10, 65);
      break;

    case 'krem':
      // Krem temada sıcak tonlar
      newHue = (h + 10) % 360; // Hafif sıcak ton
      newSaturation = Math.max(s - 5, 45);
      newLightness = isLight ? Math.max(l - 15, 30) : Math.min(l + 15, 70);
      break;
  }

  return hslToString(newHue, newSaturation, newLightness);
};

/**
 * Annotation için özel renk optimizasyonu
 */
export const optimizeColorForAnnotation = (
  baseColor: string,
  theme: Theme,
  annotationType: 'note' | 'highlight' | 'bookmark'
): string => {
  const rgb = parseColor(baseColor);
  if (!rgb) return baseColor;

  const [h, s, l] = rgbToHsl(...rgb);
  let newHue = h;
  let newSaturation = s;
  let newLightness = l;

  // Annotation tipine göre özel ayarlar
  switch (annotationType) {
    case 'note':
      // Notlar için sarımsı tonlar
      newHue = Math.abs(h - 45) < 30 ? h : (h + 45) % 360;
      newSaturation = Math.min(s + 20, 80);
      break;

    case 'bookmark':
      // Yer imleri için yeşilimsi tonlar
      newHue = Math.abs(h - 142) < 30 ? h : (h + 142) % 360;
      newSaturation = Math.min(s + 15, 75);
      break;

    case 'highlight':
      // Vurgulamalar için mevcut rengi koru ama optimize et
      newSaturation = Math.min(s + 10, 85);
      break;
  }

  // Tema bazında son ayarlar
  const optimizedBase = hslToString(newHue, newSaturation, newLightness);
  return optimizeColorForTheme(optimizedBase, theme);
};

/**
 * Text color'a göre autohue uygulayarak dinamik renk paleti oluşturur
 */
export const generateDynamicColorPalette = (baseTextColor: string = 'var(--text-color)'): Array<{
  name: string;
  id: string;
  value: string;
  background: string;
  textColor: string;
  hoverColor: string;
  muted: string;
  hueShift: number;
  saturationBoost: number;
  lightnessAdjust: number;
  originalHsl: { h: number; s: number; l: number };
  finalHsl: { h: number; s: number; l: number };
}> => {
  // Text color'ı parse et
  const textRgb = parseColor(baseTextColor);
  if (!textRgb) {
    // Fallback: varsayılan text color
    return generateFallbackColors();
  }

  const [baseR, baseG, baseB] = textRgb;
  const [baseH, baseS, baseL] = rgbToHsl(baseR, baseG, baseB);
  const isTextLight = isLightColor(baseR, baseG, baseB);

  // 8 farklı hue shift ile dinamik renkler oluştur
  const hueShifts = [0, 45, 90, 135, 180, 225, 270, 315]; // 45'er derece aralıklarla
  const colorData = [
    { name: 'Ana Renk', id: 'color_1' },
    { name: 'Sıcak Ton', id: 'color_2' },
    { name: 'Doğa Yeşili', id: 'color_3' },
    { name: 'Su Mavisi', id: 'color_4' },
    { name: 'Zıt Renk', id: 'color_5' },
    { name: 'Kraliyet Moru', id: 'color_6' },
    { name: 'Pembe Ton', id: 'color_7' },
    { name: 'Enerji Turuncu', id: 'color_8' }
  ];

  return hueShifts.map((shift, index) => {
    const colorInfo = colorData[index];

    // Hue shift uygula
    const newHue = (baseH + shift) % 360;

    // Saturation boost hesapla - text color'ın saturation'ına göre
    const saturationBoost = Math.min(30, 85 - baseS); // Maksimum 85'e kadar çıkar
    const newSaturation = Math.min(baseS + saturationBoost, 85);

    // Lightness ayarla - tema ve text color'a göre
    let lightnessAdjust = 0;
    let newLightness = baseL;
    let backgroundLightness = 85;

    if (isTextLight) {
      // Text açıksa (dark tema), renkler daha açık olsun
      lightnessAdjust = Math.min(15, 75 - baseL);
      newLightness = Math.min(baseL + lightnessAdjust, 75);
      backgroundLightness = 25;
    } else {
      // Text koyuysa (light tema), renkler daha koyu olsun
      lightnessAdjust = Math.max(-15, 35 - baseL);
      newLightness = Math.max(baseL + lightnessAdjust, 35);
      backgroundLightness = 90;
    }

    // Ana renk
    const value = hslToString(newHue, newSaturation, newLightness);

    // Background rengi - düşük saturation
    const background = hslToString(newHue, newSaturation * 0.3, backgroundLightness);

    // Text color - kontrast için
    const textColor = isTextLight ? '#ffffff' : '#000000';

    // Hover rengi - biraz daha koyu/açık
    const hoverLightness = isTextLight ?
      Math.max(backgroundLightness - 8, 15) :
      Math.min(backgroundLightness + 8, 95);
    const hoverColor = hslToString(newHue, newSaturation * 0.4, hoverLightness);

    // Muted rengi - daha yumuşak
    const mutedLightness = isTextLight ?
      Math.min(newLightness + 10, 80) :
      Math.max(newLightness - 10, 30);
    const muted = hslToString(newHue, newSaturation * 0.6, mutedLightness);

    return {
      name: colorInfo.name,
      id: colorInfo.id,
      value,
      background,
      textColor,
      hoverColor,
      muted,
      hueShift: shift,
      saturationBoost,
      lightnessAdjust,
      originalHsl: { h: baseH, s: baseS, l: baseL },
      finalHsl: { h: newHue, s: newSaturation, l: newLightness }
    };
  });
};

/**
 * Fallback renk paleti - text color parse edilemediğinde
 */
const generateFallbackColors = (): Array<{
  name: string;
  id: string;
  value: string;
  background: string;
  textColor: string;
  hoverColor: string;
  muted: string;
  hueShift: number;
  saturationBoost: number;
  lightnessAdjust: number;
  originalHsl: { h: number; s: number; l: number };
  finalHsl: { h: number; s: number; l: number };
}> => {
  const fallbackColors = [
    { name: 'Ana Renk', id: 'color_1', hue: 210, saturation: 85, lightness: 50 },
    { name: 'Sıcak Ton', id: 'color_2', hue: 45, saturation: 85, lightness: 55 },
    { name: 'Doğa Yeşili', id: 'color_3', hue: 142, saturation: 76, lightness: 50 },
    { name: 'Su Mavisi', id: 'color_4', hue: 180, saturation: 80, lightness: 50 },
    { name: 'Zıt Renk', id: 'color_5', hue: 210, saturation: 85, lightness: 50 },
    { name: 'Kraliyet Moru', id: 'color_6', hue: 271, saturation: 81, lightness: 55 },
    { name: 'Pembe Ton', id: 'color_7', hue: 328, saturation: 86, lightness: 60 },
    { name: 'Enerji Turuncu', id: 'color_8', hue: 25, saturation: 95, lightness: 55 },
  ];

  return fallbackColors.map(({ name, id, hue, saturation, lightness }, index) => ({
    name,
    id,
    value: hslToString(hue, saturation, lightness),
    background: hslToString(hue, saturation * 0.3, 90),
    textColor: '#000000',
    hoverColor: hslToString(hue, saturation * 0.4, 85),
    muted: hslToString(hue, saturation * 0.6, lightness + 10),
    hueShift: index * 45,
    saturationBoost: 0,
    lightnessAdjust: 0,
    originalHsl: { h: hue, s: saturation, l: lightness },
    finalHsl: { h: hue, s: saturation, l: lightness }
  }));
};
