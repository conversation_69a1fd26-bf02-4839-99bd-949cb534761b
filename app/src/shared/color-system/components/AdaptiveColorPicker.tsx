import React, { memo } from 'react';
import { Check, Trash2, Palette } from 'lucide-react';
import { useColorPicker, useAdaptiveOverlay } from '@shared/color-system/hooks/useColorSystem';

interface AdaptiveColorPickerProps {
  /** Seç<PERSON> renk */
  selectedColor?: string;
  /** Seçili stil tipi */
  selectedStyle?: 'background' | 'text';
  /** Renk seçildiğinde tetiklenecek fonksiyon */
  onColorSelect: (color: string, style: 'background' | 'text') => void;
  /** Özel CSS sınıfı */
  className?: string;
  /** Compact görünüm */
  compact?: boolean;
  /** Açılım yönü */
  placement?: 'above' | 'below';
}

const AdaptiveColorPicker: React.FC<AdaptiveColorPickerProps> = ({
  selectedColor,
  selectedStyle = 'background',
  onColorSelect,
  className = '',
  compact = false,
  placement = 'above'
}) => {
  const { colors, theme } = useColorPicker();
  const menuBgColor = useAdaptiveOverlay(21);
  const cardBgColor = useAdaptiveOverlay(12);
  const borderColor = useAdaptiveOverlay(8);
  const lightBorderColor = useAdaptiveOverlay(5);

  // Profesyonel boyutlandırma
  const buttonSize = 32; // Biraz daha büyük
  const gap = 10;
  const gridWidth = (buttonSize * 4) + (gap * 3);
  const containerPadding = 16;
  const totalWidth = gridWidth + (containerPadding * 2);

  // Placement'a göre border styles
  const getBorderStyles = () => {
    if (!compact) return { borderRadius: '12px' };

    if (placement === 'below') {
      return {
        borderRadius: '12px',
        borderTopWidth: '0'
      };
    } else {
      return {
        borderRadius: '12px',
        borderBottomWidth: '0'
      };
    }
  };

  // Renkleri 2 satıra böl
  const firstRow = colors.slice(0, 4);
  const secondRow = colors.slice(4, 8);

  return (
    <div
      className={`adaptive-color-picker p-4 border shadow-xl backdrop-blur-sm ${className}`}
      style={{
        backgroundColor: menuBgColor,
        borderColor: borderColor,
        width: compact ? `${totalWidth}px` : `${totalWidth + 24}px`,
        ...getBorderStyles()
      }}
    >
      {/* Header */}
      <div className="flex items-center gap-2 mb-3 pb-2 border-b" style={{ borderColor: lightBorderColor }}>
        <Palette size={14} style={{ color: 'var(--text-color)' }} />
        <span className="text-sm font-medium" style={{ color: 'var(--text-color)' }}>
          Renk Seçici
        </span>
        <span className="text-xs opacity-60 ml-auto" style={{ color: 'var(--text-color)' }}>
          {theme}
        </span>
      </div>

      {/* Text Color Section */}
      <div className="mb-4">
        <h4 className="text-xs font-semibold mb-2 opacity-80" style={{ color: 'var(--text-color)' }}>
          Metin Rengi
        </h4>
        
        {/* First row */}
        <div className="flex gap-2.5 mb-2">
          {firstRow.map((color, index) => (
            <button
              key={`text-${index}`}
              className="relative rounded-xl border-2 transition-all duration-300 hover:scale-110 hover:shadow-lg flex items-center justify-center group"
              style={{
                width: `${buttonSize}px`,
                height: `${buttonSize}px`,
                backgroundColor: cardBgColor,
                borderColor: selectedColor === color.value && selectedStyle === 'text' 
                  ? color.value 
                  : 'transparent',
                boxShadow: selectedColor === color.value && selectedStyle === 'text'
                  ? `0 0 0 2px ${color.value}40`
                  : 'none'
              }}
              onClick={() => onColorSelect(color.value, 'text')}
              title={`${color.name} - Metin`}
            >
              <span
                className="text-sm font-bold transition-transform group-hover:scale-110"
                style={{ color: color.value }}
              >
                A
              </span>
              {selectedColor === color.value && selectedStyle === 'text' && (
                <div 
                  className="absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center shadow-sm"
                  style={{ backgroundColor: color.value }}
                >
                  <Check size={8} className="text-white" />
                </div>
              )}
            </button>
          ))}
        </div>

        {/* Second row */}
        <div className="flex gap-2.5">
          {secondRow.map((color, index) => (
            <button
              key={`text-${index + 4}`}
              className="relative rounded-xl border-2 transition-all duration-300 hover:scale-110 hover:shadow-lg flex items-center justify-center group"
              style={{
                width: `${buttonSize}px`,
                height: `${buttonSize}px`,
                backgroundColor: cardBgColor,
                borderColor: selectedColor === color.value && selectedStyle === 'text' 
                  ? color.value 
                  : 'transparent',
                boxShadow: selectedColor === color.value && selectedStyle === 'text'
                  ? `0 0 0 2px ${color.value}40`
                  : 'none'
              }}
              onClick={() => onColorSelect(color.value, 'text')}
              title={`${color.name} - Metin`}
            >
              <span
                className="text-sm font-bold transition-transform group-hover:scale-110"
                style={{ color: color.value }}
              >
                A
              </span>
              {selectedColor === color.value && selectedStyle === 'text' && (
                <div 
                  className="absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center shadow-sm"
                  style={{ backgroundColor: color.value }}
                >
                  <Check size={8} className="text-white" />
                </div>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Background Color Section */}
      <div className="mb-4">
        <h4 className="text-xs font-semibold mb-2 opacity-80" style={{ color: 'var(--text-color)' }}>
          Arka Plan Rengi
        </h4>
        
        {/* First row */}
        <div className="flex gap-2.5 mb-2">
          {firstRow.map((color, index) => (
            <button
              key={`bg-${index}`}
              className="relative rounded-xl border-2 transition-all duration-300 hover:scale-110 hover:shadow-lg group"
              style={{
                width: `${buttonSize}px`,
                height: `${buttonSize}px`,
                backgroundColor: color.background,
                borderColor: selectedColor === color.value && selectedStyle === 'background' 
                  ? color.value 
                  : color.background,
                boxShadow: selectedColor === color.value && selectedStyle === 'background'
                  ? `0 0 0 2px ${color.value}60`
                  : 'none'
              }}
              onClick={() => onColorSelect(color.value, 'background')}
              title={`${color.name} - Arka Plan`}
            >
              {selectedColor === color.value && selectedStyle === 'background' && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <Check size={14} className="text-white drop-shadow-lg" style={{ color: color.textColor }} />
                </div>
              )}
            </button>
          ))}
        </div>

        {/* Second row */}
        <div className="flex gap-2.5">
          {secondRow.map((color, index) => (
            <button
              key={`bg-${index + 4}`}
              className="relative rounded-xl border-2 transition-all duration-300 hover:scale-110 hover:shadow-lg group"
              style={{
                width: `${buttonSize}px`,
                height: `${buttonSize}px`,
                backgroundColor: color.background,
                borderColor: selectedColor === color.value && selectedStyle === 'background' 
                  ? color.value 
                  : color.background,
                boxShadow: selectedColor === color.value && selectedStyle === 'background'
                  ? `0 0 0 2px ${color.value}60`
                  : 'none'
              }}
              onClick={() => onColorSelect(color.value, 'background')}
              title={`${color.name} - Arka Plan`}
            >
              {selectedColor === color.value && selectedStyle === 'background' && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <Check size={14} className="text-white drop-shadow-lg" style={{ color: color.textColor }} />
                </div>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Clear Button */}
      <div className="pt-3 border-t" style={{ borderColor: lightBorderColor }}>
        <button
          className="w-full flex items-center justify-center gap-2 py-2.5 rounded-xl border-2 transition-all duration-300 hover:scale-105 hover:shadow-md group"
          style={{
            backgroundColor: cardBgColor,
            borderColor: borderColor,
            color: 'var(--text-color)',
            maxWidth: `${gridWidth}px`
          }}
          onClick={() => onColorSelect('DELETE', 'background')}
        >
          <Trash2 size={14} className="group-hover:text-red-500 transition-colors" />
          <span className="text-sm font-medium">Temizle</span>
        </button>
      </div>
    </div>
  );
};

export default memo(AdaptiveColorPicker);
