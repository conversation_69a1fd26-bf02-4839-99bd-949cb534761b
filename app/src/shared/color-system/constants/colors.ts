import type { ColorDefinition, ThemeLightnessMap } from '../types';

/**
 * 🎨 Color System Constants
 * Renk sistemi için tüm sabitler
 */

// Ana renk paleti - HSL tabanlı
export const COLOR_DEFINITIONS: ColorDefinition[] = [
  // Primary colors - <PERSON> renkler
  { 
    hue: 210, 
    saturation: 85, 
    name: 'blue', 
    category: 'primary',
    description: 'Ana mavi renk - linkler, butonlar için'
  },
  { 
    hue: 142, 
    saturation: 76, 
    name: 'green', 
    category: 'primary',
    description: 'Ana yeşil renk - başarı mesajları için'
  },
  { 
    hue: 38, 
    saturation: 92, 
    name: 'yellow', 
    category: 'primary',
    description: 'Ana sarı renk - uyarılar için'
  },
  
  // Semantic colors - Anlamsal renkler
  { 
    hue: 0, 
    saturation: 84, 
    name: 'red', 
    category: 'semantic',
    description: 'Hata mesajları ve tehlike durumları'
  },
  { 
    hue: 25, 
    saturation: 95, 
    name: 'orange', 
    category: 'semantic',
    description: '<PERSON>yarı mesajları ve dikkat çekici öğeler'
  },
  { 
    hue: 271, 
    saturation: 81, 
    name: 'purple', 
    category: 'semantic',
    description: 'Premium özellikler ve özel durumlar'
  },
  
  // Accent colors - Vurgu renkleri
  { 
    hue: 328, 
    saturation: 86, 
    name: 'pink', 
    category: 'accent',
    description: 'Dekoratif ve vurgu amaçlı'
  },
  { 
    hue: 174, 
    saturation: 72, 
    name: 'teal', 
    category: 'accent',
    description: 'Sakinleştirici ve modern görünüm'
  },
];

// Tema bazında parlaklık değerleri
export const THEME_LIGHTNESS_MAP: ThemeLightnessMap = {
  dark: {
    original: 65,  // Koyu temada daha açık
    light: 75,
    dark: 45,
    muted: 55,
    contrast: 85   // Maksimum kontrast
  },
  light: {
    original: 50,  // Açık temada orta ton
    light: 70,
    dark: 30,
    muted: 60,
    contrast: 25   // Maksimum kontrast
  },
  krem: {
    original: 45,  // Krem temada daha koyu
    light: 65,
    dark: 25,
    muted: 55,
    contrast: 20   // Maksimum kontrast
  }
};

// Renk erişilebilirlik standartları
export const ACCESSIBILITY_STANDARDS = {
  WCAG_AA_NORMAL: 4.5,
  WCAG_AA_LARGE: 3.0,
  WCAG_AAA_NORMAL: 7.0,
  WCAG_AAA_LARGE: 4.5,
} as const;

// Varsayılan renk geçiş süreleri
export const COLOR_TRANSITIONS = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
} as const;

// Renk kategorileri için varsayılan ayarlar
export const CATEGORY_DEFAULTS = {
  primary: {
    saturationMultiplier: 1.0,
    lightnessOffset: 0,
  },
  semantic: {
    saturationMultiplier: 0.9,
    lightnessOffset: -5,
  },
  accent: {
    saturationMultiplier: 0.8,
    lightnessOffset: 5,
  },
} as const;

// Highlight renkleri için özel ayarlar
export const HIGHLIGHT_SETTINGS = {
  backgroundOpacity: 0.15,
  textOpacity: 1.0,
  hoverOpacity: 0.25,
  borderOpacity: 0.3,
} as const;

// Overlay yoğunluk seviyeleri
export const OVERLAY_INTENSITIES = {
  SUBTLE: 3,
  LIGHT: 8,
  MEDIUM: 15,
  STRONG: 25,
  HEAVY: 35,
} as const;

// Tema geçiş haritası
export const THEME_CYCLE_MAP = {
  dark: 'light',
  light: 'krem',
  krem: 'dark',
} as const;

// CSS değişken isimleri
export const CSS_VARIABLES = {
  TEXT_COLOR: '--text-color',
  BG_COLOR: '--bg-color',
  ACCENT_COLOR: '--accent-color',
  BORDER_COLOR: '--border-color',
} as const;
