import { useState, useEffect } from 'react';

/**
 * RGB'den HSL'e dönüştürme fonksiyonu
 */
const rgbToHsl = (r: number, g: number, b: number): [number, number, number] => {
  r /= 255;
  g /= 255;
  b /= 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  return [h * 360, s * 100, l * 100];
};

/**
 * HSL'den RGB'ye dönüştürme fonksiyonu
 */
const hslToRgb = (h: number, s: number, l: number): [number, number, number] => {
  h /= 360;
  s /= 100;
  l /= 100;

  const hue2rgb = (p: number, q: number, t: number): number => {
    if (t < 0) t += 1;
    if (t > 1) t -= 1;
    if (t < 1/6) return p + (q - p) * 6 * t;
    if (t < 1/2) return q;
    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
    return p;
  };

  if (s === 0) {
    return [l * 255, l * 255, l * 255]; // achromatic
  }

  const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
  const p = 2 * l - q;
  const r = hue2rgb(p, q, h + 1/3);
  const g = hue2rgb(p, q, h);
  const b = hue2rgb(p, q, h - 1/3);

  return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)];
};

/**
 * CSS renk değerini RGB değerlerine çevirir
 */
const parseColor = (color: string): [number, number, number] | null => {
  try {
    // CSS variable ise değerini al
    if (color.startsWith('var(')) {
      const root = document.documentElement;
      const varName = color.replace('var(', '').replace(')', '');
      color = getComputedStyle(root).getPropertyValue(varName).trim();
    }

    // Hex format
    if (color.startsWith('#')) {
      const hex = color.slice(1);
      if (hex.length === 3) {
        return [
          parseInt(hex[0] + hex[0], 16),
          parseInt(hex[1] + hex[1], 16),
          parseInt(hex[2] + hex[2], 16)
        ];
      } else if (hex.length === 6) {
        return [
          parseInt(hex.slice(0, 2), 16),
          parseInt(hex.slice(2, 4), 16),
          parseInt(hex.slice(4, 6), 16)
        ];
      }
    }

    // RGB format
    const rgbMatch = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
    if (rgbMatch) {
      return [
        parseInt(rgbMatch[1]),
        parseInt(rgbMatch[2]),
        parseInt(rgbMatch[3])
      ];
    }

    return null;
  } catch (error) {
    console.error('Color parsing error:', error);
    return null;
  }
};

/**
 * Rengin parlaklığını kontrol eder (YIQ formülü)
 */
const isLightColor = (r: number, g: number, b: number): boolean => {
  const yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;
  return yiq >= 128;
};

/**
 * HSL tabanlı gelişmiş renk manipülasyonu
 * Kontrolü, öngörülebilirlik ve yan etkisizlik sağlar
 */
export const useEnhancedHue = (baseColor: string): string => {
  const [enhancedColor, setEnhancedColor] = useState<string>('');

  useEffect(() => {
    const updateColor = () => {
      const rgb = parseColor(baseColor);
      if (!rgb) {
        setEnhancedColor(baseColor);
        return;
      }

      const [r, g, b] = rgb;
      const [h, s, l] = rgbToHsl(r, g, b);
      const isLight = isLightColor(r, g, b);

      // HSL manipülasyonu - kontrollü ve öngörülebilir
      let newHue = h;
      let newSaturation = s;
      let newLightness = l;

      // Hue (Ton) ayarı - uyumlu renk paleti için
      newHue = (h + 45) % 360; // 45° kaydırma - uyumlu renk

      // Saturation (Canlılık) artırma - daha dikkat çekici
      newSaturation = Math.min(s + 25, 85); // Maksimum %85'e kadar

      // Lightness (Parlaklık) ayarı - okunabilirlik için
      if (isLight) {
        // Açık temada daha koyu yap
        newLightness = Math.max(l - 15, 25);
      } else {
        // Koyu temada daha açık yap
        newLightness = Math.min(l + 20, 75);
      }

      // HSL'den RGB'ye çevir ve CSS formatına dönüştür
      const [newR, newG, newB] = hslToRgb(newHue, newSaturation, newLightness);
      const finalColor = `rgb(${newR}, ${newG}, ${newB})`;

      setEnhancedColor(finalColor);
    };

    updateColor();

    // CSS değişkenlerini izle
    const observer = new MutationObserver(updateColor);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['style']
    });

    return () => observer.disconnect();
  }, [baseColor]);

  return enhancedColor;
};

/**
 * @deprecated Legacy autoHue function - Use useEnhancedHue hook instead
 *
 * ⚠️ Bu fonksiyon deprecated! Yeni projeler için useEnhancedHue kullanın:
 *
 * ```tsx
 * // ❌ ESKİ YÖNTEM
 * const style = autoHue('var(--text-color)');
 *
 * // ✅ YENİ YÖNTEM
 * const enhancedColor = useEnhancedHue('var(--text-color)');
 * const style = { color: enhancedColor };
 * ```
 */
export const autoHue = (baseColor: string): { color: string; filter: string } => {
  const mixedColor = `color-mix(in srgb, ${baseColor} 49%, orange 51%)`;
  const filterValue = 'hue-rotate(175deg) brightness(1.0)';

  return {
    color: mixedColor,
    filter: filterValue,
  };
};