import { useState, useCallback, useEffect, useRef } from 'react';
import useErrorHandler from './useErrorHandler';

interface UseSafeAsyncOptions {
  onSuccess?: (data: unknown) => void;
  onError?: (error: Error) => void;
  errorContext?: string;
  retryCount?: number;
  retryDelay?: number;
}

interface UseSafeAsyncReturn<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  errorType: 'network' | 'content' | 'auth' | 'permission' | 'general';
  execute: (...args: any[]) => Promise<T | null>;
  reset: () => void;
  retry: () => void;
}

/**
 * Asenkron işlemleri güvenli bir şekilde yönetmek için hook.
 * Hata yönetimi, yeniden deneme ve temizleme işlemlerini otomatikleştirir.
 * 
 * @param asyncFunction Çalıştırılacak asenkron fonksiyon
 * @param options Konfigürasyon seçenekleri
 */
export function useSafeAsync<T>(
  asyncFunction: (...args: any[]) => Promise<T>,
  options: UseSafeAsyncOptions = {}
): UseSafeAsyncReturn<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { handleError, error, errorType, clearError } = useErrorHandler();
  
  const isMountedRef = useRef<boolean>(true);
  const lastArgsRef = useRef<any[]>([]);
  const retryCountRef = useRef<number>(0);
  const maxRetries = options.retryCount || 2;
  const retryDelay = options.retryDelay || 1000;

  // Component unmount olduğunda isMountedRef'i false yap
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Asenkron fonksiyonu çalıştır
  const execute = useCallback(
    async (...args: any[]): Promise<T | null> => {
      // Önceki hataları temizle
      clearError();
      setLoading(true);
      
      // Son argümanları sakla (retry için)
      lastArgsRef.current = args;
      
      try {
        const result = await asyncFunction(...args);
        
        // Component hala mount edilmişse state'i güncelle
        if (isMountedRef.current) {
          setData(result);
          setLoading(false);
          
          // Başarı callback'i
          if (options.onSuccess) {
            options.onSuccess(result);
          }
          
          // Yeniden deneme sayacını sıfırla
          retryCountRef.current = 0;
        }
        
        return result;
      } catch (err) {
        // Component hala mount edilmişse hata state'ini güncelle
        if (isMountedRef.current) {
          handleError(err, options.errorContext);
          setLoading(false);
          
          // Hata callback'i
          if (options.onError && err instanceof Error) {
            options.onError(err);
          }
        }
        
        return null;
      }
    },
    [asyncFunction, clearError, handleError, options]
  );

  // State'i sıfırla
  const reset = useCallback(() => {
    setData(null);
    setLoading(false);
    clearError();
    retryCountRef.current = 0;
  }, [clearError]);

  // Son çağrıyı tekrar dene
  const retry = useCallback(async () => {
    if (retryCountRef.current >= maxRetries) {
      console.warn(`Maximum retry count (${maxRetries}) reached.`);
      return;
    }
    
    retryCountRef.current += 1;
    console.log(`Retrying... Attempt ${retryCountRef.current} of ${maxRetries}`);
    
    // Retry delay
    await new Promise(resolve => setTimeout(resolve, retryDelay));
    
    // Son argümanlarla tekrar çalıştır
    return execute(...lastArgsRef.current);
  }, [execute, maxRetries, retryDelay]);

  return {
    data,
    loading,
    error,
    errorType,
    execute,
    reset,
    retry
  };
}

export default useSafeAsync;
