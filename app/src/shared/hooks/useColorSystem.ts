import { useState, useEffect, useMemo } from 'react';

/**
 * ⚠️ DEPRECATED: Bu dosya app/src/shared/color-system/hooks/useColorSystem.ts'ye taşındı
 * Lütfen yeni lokasyonu kullanın
 */

/**
 * 🎨 Adaptive Color System
 * Tema-aware, erişilebilir ve yeniden kullanılabilir renk sistemi
 */

// Temel renk tanımları (HSL formatında)
interface ColorDefinition {
  hue: number;        // 0-360
  saturation: number; // 0-100
  name: string;
  category: 'primary' | 'semantic' | 'accent';
}

// Renk varyantları
interface ColorVariant {
  original: string;
  light: string;
  dark: string;
  muted: string;
  contrast: string; // Yüksek kontrast versiyonu
}

// Tema tipi
type Theme = 'dark' | 'light' | 'krem';

// Ana renk paleti - HSL tabanlı
const COLOR_DEFINITIONS: ColorDefinition[] = [
  // Primary colors
  { hue: 210, saturation: 85, name: 'blue', category: 'primary' },
  { hue: 142, saturation: 76, name: 'green', category: 'primary' },
  { hue: 38, saturation: 92, name: 'yellow', category: 'primary' },
  
  // Semantic colors
  { hue: 0, saturation: 84, name: 'red', category: 'semantic' },
  { hue: 25, saturation: 95, name: 'orange', category: 'semantic' },
  { hue: 271, saturation: 81, name: 'purple', category: 'semantic' },
  
  // Accent colors
  { hue: 328, saturation: 86, name: 'pink', category: 'accent' },
  { hue: 174, saturation: 72, name: 'teal', category: 'accent' },
];

/**
 * Tema bazında parlaklık değerlerini hesaplar
 */
const getThemeBasedLightness = (theme: Theme, variant: 'original' | 'light' | 'dark' | 'muted' | 'contrast'): number => {
  const lightnessMap = {
    dark: {
      original: 65,  // Koyu temada daha açık
      light: 75,
      dark: 45,
      muted: 55,
      contrast: 85   // Maksimum kontrast
    },
    light: {
      original: 50,  // Açık temada orta ton
      light: 70,
      dark: 30,
      muted: 60,
      contrast: 25   // Maksimum kontrast
    },
    krem: {
      original: 45,  // Krem temada daha koyu
      light: 65,
      dark: 25,
      muted: 55,
      contrast: 20   // Maksimum kontrast
    }
  };

  return lightnessMap[theme][variant];
};

/**
 * HSL'den CSS renk stringine çevirir
 */
const hslToString = (h: number, s: number, l: number): string => {
  return `hsl(${h}, ${s}%, ${l}%)`;
};

/**
 * Mevcut temayı algılar
 */
const getCurrentTheme = (): Theme => {
  const root = document.documentElement;
  const bgColor = getComputedStyle(root).getPropertyValue('--bg-color').trim();
  
  // Basit tema algılama
  if (bgColor === '#121212') return 'dark';
  if (bgColor === '#F7F2E2') return 'krem';
  return 'light';
};

/**
 * Ana renk sistemi hook'u
 */
export const useColorSystem = () => {
  const [currentTheme, setCurrentTheme] = useState<Theme>(getCurrentTheme);

  // Tema değişikliklerini izle
  useEffect(() => {
    const updateTheme = () => {
      setCurrentTheme(getCurrentTheme());
    };

    const observer = new MutationObserver(updateTheme);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['style']
    });

    return () => observer.disconnect();
  }, []);

  // Renk paletini oluştur
  const colorPalette = useMemo(() => {
    const palette: Record<string, ColorVariant> = {};

    COLOR_DEFINITIONS.forEach(({ hue, saturation, name }) => {
      palette[name] = {
        original: hslToString(hue, saturation, getThemeBasedLightness(currentTheme, 'original')),
        light: hslToString(hue, saturation * 0.7, getThemeBasedLightness(currentTheme, 'light')),
        dark: hslToString(hue, saturation, getThemeBasedLightness(currentTheme, 'dark')),
        muted: hslToString(hue, saturation * 0.5, getThemeBasedLightness(currentTheme, 'muted')),
        contrast: hslToString(hue, saturation, getThemeBasedLightness(currentTheme, 'contrast'))
      };
    });

    return palette;
  }, [currentTheme]);

  // Highlight için özel renk paleti
  const highlightColors = useMemo(() => {
    return COLOR_DEFINITIONS.map(({ name }) => ({
      name: name.charAt(0).toUpperCase() + name.slice(1),
      value: colorPalette[name].original,
      background: colorPalette[name].light,
      muted: colorPalette[name].muted
    }));
  }, [colorPalette]);

  // Utility fonksiyonlar
  const getColor = (colorName: string, variant: keyof ColorVariant = 'original') => {
    return colorPalette[colorName]?.[variant] || colorPalette.blue.original;
  };

  const getContrastColor = (colorName: string) => {
    return colorPalette[colorName]?.contrast || colorPalette.blue.contrast;
  };

  return {
    // Ana renk paleti
    colors: colorPalette,
    
    // Highlight renkleri
    highlightColors,
    
    // Mevcut tema
    currentTheme,
    
    // Utility fonksiyonlar
    getColor,
    getContrastColor,
    
    // Özel renk grupları
    primaryColors: COLOR_DEFINITIONS.filter(c => c.category === 'primary').map(c => c.name),
    semanticColors: COLOR_DEFINITIONS.filter(c => c.category === 'semantic').map(c => c.name),
    accentColors: COLOR_DEFINITIONS.filter(c => c.category === 'accent').map(c => c.name),
  };
};

/**
 * Renk picker için özel hook
 */
export const useColorPicker = () => {
  const { highlightColors, getColor, currentTheme } = useColorSystem();

  // ColorPicker için optimize edilmiş renk listesi
  const pickerColors = highlightColors.map(color => ({
    ...color,
    textColor: getColor(color.name.toLowerCase(), 'contrast'),
    hoverColor: getColor(color.name.toLowerCase(), 'muted')
  }));

  return {
    colors: pickerColors,
    theme: currentTheme,
    getColor
  };
};

/**
 * Tema-aware overlay hook'u (autoOverlay'in gelişmiş versiyonu)
 */
export const useAdaptiveOverlay = (intensity: number = 10) => {
  const { currentTheme } = useColorSystem();
  
  const [overlayColor, setOverlayColor] = useState<string>('');

  useEffect(() => {
    const updateOverlay = () => {
      const baseColor = 'var(--bg-color)';
      const mixColor = currentTheme === 'light' ? 'black' : 'white';
      setOverlayColor(`color-mix(in srgb, ${baseColor} ${100 - intensity}%, ${mixColor})`);
    };

    updateOverlay();

    const observer = new MutationObserver(updateOverlay);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['style']
    });

    return () => observer.disconnect();
  }, [intensity, currentTheme]);

  return overlayColor;
};
