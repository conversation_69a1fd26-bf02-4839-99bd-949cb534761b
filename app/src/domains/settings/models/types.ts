import { ThemeMode } from '@shared/theme/definitions';

// Uygulama ayarları
export interface IAppSettings {
  theme: ThemeMode;
  language: string;
  showWelcomeScreen: boolean;
  enableNotifications: boolean;
  enableAutoSync: boolean;
  syncInterval: number; // milliseconds
  lastSyncDate?: Date;
  customColors?: {
    textColor?: string;
    backgroundColor?: string;
    enableCustomColors?: boolean;
  };
}

// Kullanıcı profili
export interface IUserProfile {
  id: number;
  username: string;
  email?: string;
  name?: string;
  avatar?: string;
  createdAt: Date;
  lastLoginAt?: Date;
  preferences: IUserPreferences;
}

// Kullanıcı tercihleri
export interface IUserPreferences {
  defaultBookCategory?: number;
  defaultReadingSettings: {
    fontSize: number;
    fontFamily: string;
    textAlignment: 'left' | 'center' | 'right' | 'justify';
  };
  showRecentBooks: boolean;
  showReadingProgress: boolean;
  showReadingStats: boolean;
  enableDailyReminder: boolean;
  reminderTime?: string; // HH:MM format
}

// Yerelleştirme ayarları
export interface ILocalizationSettings {
  language: string;
  dateFormat: string;
  timeFormat: string;
  timezone: string;
  translationEnabled: boolean;
}

// Bildirim ayarları
export interface INotificationSettings {
  enable: boolean;
  dailyReminder: boolean;
  reminderTime: string; // HH:MM format
  newContentUpdates: boolean;
  systemAnnouncements: boolean;
  sound: boolean;
  vibration: boolean;
}

// Yedekleme ve senkronizasyon ayarları
export interface ISyncSettings {
  autoSync: boolean;
  syncInterval: number; // milliseconds
  syncOnWifi: boolean;
  syncReadingProgress: boolean;
  syncBookmarks: boolean;
  syncNotes: boolean;
  syncSettings: boolean;
  lastSyncDate?: Date;
}

// Erişilebilirlik ayarları
export interface IAccessibilitySettings {
  highContrast: boolean;
  largeText: boolean;
  reduceAnimation: boolean;
  screenReader: boolean;
}

// UI için loading state'leri
export interface ISettingsLoadingState {
  appSettings: boolean;
  userProfile: boolean;
  localizationSettings: boolean;
  notificationSettings: boolean;
  syncSettings: boolean;
  accessibilitySettings: boolean;
} 