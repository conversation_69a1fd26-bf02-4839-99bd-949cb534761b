import { create } from 'zustand';
import {
  IAppSettings,
  IUserProfile,
  IUserPreferences,
  ISettingsLoadingState,
  ILocalizationSettings,
  INotificationSettings,
  ISyncSettings,
  IAccessibilitySettings
} from '../models/types';
import { settingsService } from '../services/settingsservice';
import { ThemeMode } from '@shared/theme/definitions';

// Settings domain state'ini ve işlevlerini yöneten Zustand store
interface SettingsState {
  // State
  appSettings: IAppSettings;
  userProfile: IUserProfile | null;
  localizationSettings: ILocalizationSettings;
  notificationSettings: INotificationSettings;
  syncSettings: ISyncSettings;
  accessibilitySettings: IAccessibilitySettings;
  loading: ISettingsLoadingState;
  
  // App settings actions
  fetchAppSettings: () => Promise<void>;
  updateAppSettings: (settings: Partial<IAppSettings>) => Promise<void>;
  
  // User profile actions
  fetchUserProfile: (userId: number) => Promise<void>;
  updateUserPreferences: (userId: number, preferences: Partial<IUserPreferences>) => Promise<void>;
  
  // Theme actions
  changeTheme: (theme: ThemeMode) => Promise<void>;
  
  // Custom color actions
  updateCustomColors: (colors: { textColor?: string; backgroundColor?: string; enableCustomColors?: boolean }) => Promise<void>;
  resetCustomColors: () => Promise<void>;
  
  // Language actions
  changeLanguage: (language: string) => Promise<void>;
  
  // Localization settings actions
  fetchLocalizationSettings: () => Promise<void>;
  updateLocalizationSettings: (settings: Partial<ILocalizationSettings>) => Promise<void>;
  
  // Notification settings actions
  fetchNotificationSettings: () => Promise<void>;
  updateNotificationSettings: (settings: Partial<INotificationSettings>) => Promise<void>;
  
  // Sync settings actions
  fetchSyncSettings: () => Promise<void>;
  updateSyncSettings: (settings: Partial<ISyncSettings>) => Promise<void>;
  
  // Accessibility settings actions
  fetchAccessibilitySettings: () => Promise<void>;
  updateAccessibilitySettings: (settings: Partial<IAccessibilitySettings>) => Promise<void>;
  
  // Reset all settings
  resetAllSettings: () => Promise<void>;
}

// Default loading state
const defaultLoadingState: ISettingsLoadingState = {
  appSettings: false,
  userProfile: false,
  localizationSettings: false,
  notificationSettings: false,
  syncSettings: false,
  accessibilitySettings: false
};

// Varsayılan uygulama ayarları - settingsService'den alınabilir
const defaultAppSettings: IAppSettings = {
  theme: ThemeMode.LIGHT,
  language: 'tr',
  showWelcomeScreen: true,
  enableNotifications: true,
  enableAutoSync: true,
  syncInterval: 24 * 60 * 60 * 1000, // 24 saat (ms cinsinden)
  customColors: {
    textColor: undefined,
    backgroundColor: undefined,
    enableCustomColors: false
  }
};

// Diğer varsayılan ayarlar da benzer şekilde tanımlanabilir
// Buraya settingsService'deki varsayılan değerleri de aktarabiliriz

export const useSettingsStore = create<SettingsState>((set, get) => ({
  // Initial state
  appSettings: defaultAppSettings,
  userProfile: null,
  localizationSettings: {
    language: 'tr',
    dateFormat: 'DD.MM.YYYY',
    timeFormat: 'HH:mm',
    timezone: 'Europe/Istanbul',
    translationEnabled: true,
  },
  notificationSettings: {
    enable: true,
    dailyReminder: false,
    reminderTime: '20:00',
    newContentUpdates: true,
    systemAnnouncements: true,
    sound: true,
    vibration: true,
  },
  syncSettings: {
    autoSync: true,
    syncInterval: 24 * 60 * 60 * 1000,
    syncOnWifi: true,
    syncReadingProgress: true,
    syncBookmarks: true,
    syncNotes: true,
    syncSettings: true,
  },
  accessibilitySettings: {
    highContrast: false,
    largeText: false,
    reduceAnimation: false,
    screenReader: false,
  },
  loading: defaultLoadingState,
  
  // App settings actions
  fetchAppSettings: async () => {
    set(state => ({ loading: { ...state.loading, appSettings: true } }));
    
    try {
      const settings = await settingsService.getAppSettings();
      set({ 
        appSettings: settings,
        loading: { ...get().loading, appSettings: false }
      });
    } catch (error) {
      console.error('Failed to fetch app settings:', error);
      set(state => ({ loading: { ...state.loading, appSettings: false } }));
    }
  },
  
  updateAppSettings: async (settings: Partial<IAppSettings>) => {
    set(state => ({ loading: { ...state.loading, appSettings: true } }));
    
    try {
      const updatedSettings = { ...get().appSettings, ...settings };
      await settingsService.saveAppSettings(updatedSettings);
      
      set({ 
        appSettings: updatedSettings,
        loading: { ...get().loading, appSettings: false }
      });
    } catch (error) {
      console.error('Failed to update app settings:', error);
      set(state => ({ loading: { ...state.loading, appSettings: false } }));
    }
  },
  
  // User profile actions
  fetchUserProfile: async (userId: number) => {
    set(state => ({ loading: { ...state.loading, userProfile: true } }));
    
    try {
      const profile = await settingsService.getUserProfile(userId);
      set({ 
        userProfile: profile,
        loading: { ...get().loading, userProfile: false }
      });
    } catch (error) {
      console.error('Failed to fetch user profile:', error);
      set(state => ({ loading: { ...state.loading, userProfile: false } }));
    }
  },
  
  updateUserPreferences: async (userId: number, preferences: Partial<IUserPreferences>) => {
    set(state => ({ loading: { ...state.loading, userProfile: true } }));
    
    try {
      const updatedPreferences = await settingsService.updateUserPreferences(userId, preferences);
      
      // Eğer kullanıcı profili yüklenmişse, tercihleri güncelle
      if (get().userProfile) {
        set(state => ({ 
          userProfile: state.userProfile ? {
            ...state.userProfile,
            preferences: updatedPreferences
          } : null,
          loading: { ...state.loading, userProfile: false }
        }));
      } else {
        set(state => ({ loading: { ...state.loading, userProfile: false } }));
      }
    } catch (error) {
      console.error('Failed to update user preferences:', error);
      set(state => ({ loading: { ...state.loading, userProfile: false } }));
    }
  },
  
  // Theme actions
  changeTheme: async (theme: ThemeMode) => {
    set(state => ({ loading: { ...state.loading, appSettings: true } }));
    
    try {
      await settingsService.changeTheme(theme);
      set(state => ({ 
        appSettings: { ...state.appSettings, theme },
        loading: { ...state.loading, appSettings: false }
      }));
    } catch (error) {
      console.error('Failed to change theme:', error);
      set(state => ({ loading: { ...state.loading, appSettings: false } }));
    }
  },

  // Custom color actions
  updateCustomColors: async (colors: { textColor?: string; backgroundColor?: string; enableCustomColors?: boolean }) => {
    set(state => ({ loading: { ...state.loading, appSettings: true } }));
    
    try {
      const updatedSettings = { 
        ...get().appSettings, 
        customColors: { 
          ...get().appSettings.customColors, 
          ...colors 
        } 
      };
      await settingsService.saveAppSettings(updatedSettings);
      
      set({ 
        appSettings: updatedSettings,
        loading: { ...get().loading, appSettings: false }
      });
    } catch (error) {
      console.error('Failed to update custom colors:', error);
      set(state => ({ loading: { ...state.loading, appSettings: false } }));
    }
  },

  resetCustomColors: async () => {
    set(state => ({ loading: { ...state.loading, appSettings: true } }));
    
    try {
      const updatedSettings = { 
        ...get().appSettings, 
        customColors: {
          textColor: undefined,
          backgroundColor: undefined,
          enableCustomColors: false
        } 
      };
      await settingsService.saveAppSettings(updatedSettings);
      
      set({ 
        appSettings: updatedSettings,
        loading: { ...get().loading, appSettings: false }
      });
    } catch (error) {
      console.error('Failed to reset custom colors:', error);
      set(state => ({ loading: { ...state.loading, appSettings: false } }));
    }
  },
  
  // Language actions
  changeLanguage: async (language: string) => {
    set(state => ({ loading: { ...state.loading, appSettings: true } }));
    
    try {
      await settingsService.changeLanguage(language);
      set(state => ({ 
        appSettings: { ...state.appSettings, language },
        loading: { ...state.loading, appSettings: false }
      }));
    } catch (error) {
      console.error('Failed to change language:', error);
      set(state => ({ loading: { ...state.loading, appSettings: false } }));
    }
  },
  
  // Localization settings actions
  fetchLocalizationSettings: async () => {
    set(state => ({ loading: { ...state.loading, localizationSettings: true } }));
    
    try {
      const settings = await settingsService.getLocalizationSettings();
      set({ 
        localizationSettings: settings,
        loading: { ...get().loading, localizationSettings: false }
      });
    } catch (error) {
      console.error('Failed to fetch localization settings:', error);
      set(state => ({ loading: { ...state.loading, localizationSettings: false } }));
    }
  },
  
  updateLocalizationSettings: async (settings: Partial<ILocalizationSettings>) => {
    set(state => ({ 
      localizationSettings: { ...state.localizationSettings, ...settings } 
    }));
    // Burada gerçek bir API çağrısı yapılacak
  },
  
  // Notification settings actions
  fetchNotificationSettings: async () => {
    set(state => ({ loading: { ...state.loading, notificationSettings: true } }));
    
    try {
      const settings = await settingsService.getNotificationSettings();
      set({ 
        notificationSettings: settings,
        loading: { ...get().loading, notificationSettings: false }
      });
    } catch (error) {
      console.error('Failed to fetch notification settings:', error);
      set(state => ({ loading: { ...state.loading, notificationSettings: false } }));
    }
  },
  
  updateNotificationSettings: async (settings: Partial<INotificationSettings>) => {
    set(state => ({ 
      notificationSettings: { ...state.notificationSettings, ...settings } 
    }));
    // Burada gerçek bir API çağrısı yapılacak
  },
  
  // Sync settings actions
  fetchSyncSettings: async () => {
    set(state => ({ loading: { ...state.loading, syncSettings: true } }));
    
    try {
      const settings = await settingsService.getSyncSettings();
      set({ 
        syncSettings: settings,
        loading: { ...get().loading, syncSettings: false }
      });
    } catch (error) {
      console.error('Failed to fetch sync settings:', error);
      set(state => ({ loading: { ...state.loading, syncSettings: false } }));
    }
  },
  
  updateSyncSettings: async (settings: Partial<ISyncSettings>) => {
    set(state => ({ 
      syncSettings: { ...state.syncSettings, ...settings } 
    }));
    // Burada gerçek bir API çağrısı yapılacak
  },
  
  // Accessibility settings actions
  fetchAccessibilitySettings: async () => {
    set(state => ({ loading: { ...state.loading, accessibilitySettings: true } }));
    
    try {
      const settings = await settingsService.getAccessibilitySettings();
      set({ 
        accessibilitySettings: settings,
        loading: { ...get().loading, accessibilitySettings: false }
      });
    } catch (error) {
      console.error('Failed to fetch accessibility settings:', error);
      set(state => ({ loading: { ...state.loading, accessibilitySettings: false } }));
    }
  },
  
  updateAccessibilitySettings: async (settings: Partial<IAccessibilitySettings>) => {
    set(state => ({ 
      accessibilitySettings: { ...state.accessibilitySettings, ...settings } 
    }));
    // Burada gerçek bir API çağrısı yapılacak
  },
  
  // Reset all settings
  resetAllSettings: async () => {
    set(state => ({ 
      loading: { 
        ...state.loading, 
        appSettings: true,
        localizationSettings: true,
        notificationSettings: true,
        syncSettings: true,
        accessibilitySettings: true
      } 
    }));
    
    try {
      await settingsService.resetAllSettings();
      
      // Tüm ayarları yeniden yükle
      await get().fetchAppSettings();
      await get().fetchLocalizationSettings();
      await get().fetchNotificationSettings();
      await get().fetchSyncSettings();
      await get().fetchAccessibilitySettings();
    } catch (error) {
      console.error('Failed to reset all settings:', error);
      set(state => ({ 
        loading: { 
          ...state.loading, 
          appSettings: false,
          localizationSettings: false,
          notificationSettings: false,
          syncSettings: false,
          accessibilitySettings: false
        } 
      }));
    }
  }
})); 