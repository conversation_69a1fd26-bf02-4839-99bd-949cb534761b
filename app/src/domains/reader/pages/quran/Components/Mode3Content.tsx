import React from 'react';
import { CombinedVerseData, VerseWord, ITranslator } from '@reader/models/types';
import { TranslationDisplay } from './TranslationDisplay';
import { useEnhancedColorHue } from '@shared/color-system';
import { MoreVertical } from 'lucide-react';

interface Mode3ContentProps {
  verse: CombinedVerseData;
  verseBorderColor: string;
  translatorTitleColor: string;
  sectionLeftBorderColor: string;
  footnoteColor: string;
  verseNumberBg: string;
  selectedTranslators: string[];
  availableTranslators: ITranslator[];
  onVerseMenuClick?: (verseKey: string, event: React.MouseEvent) => void;
  openMenuVerseKey?: string | null;
  onCloseVerseMenu?: () => void;
  surahName?: string;
}

export const Mode3Content: React.FC<Mode3ContentProps> = ({
  verse,
  verseBorderColor,
  translatorTitleColor,
  sectionLeftBorderColor,
  footnoteColor,
  verseNumberBg,
  selectedTranslators,
  availableTranslators,
  onVerseMenuClick,
  openMenuVerseKey: _openMenuVerseKey,
  onCloseVerseMenu: _onCloseVerseMenu,
  surahName: _surahName,
}) => {
  const verseKey = `${verse.verse_no}`;

  // Enhanced color for alternating words
  const enhancedWordColor = useEnhancedColorHue('var(--text-color)');

  return (
    <div className="space-y-4 pb-5 mb-3 border-b px-2 pt-2 relative" style={{ borderColor: verseBorderColor }}>
      <div dir="rtl" className="text-right flex flex-wrap items-start gap-x-3 gap-y-3 justify-start">
        {verse.words?.map((word: VerseWord, wordIdx: number) => {
          const wordStyle = wordIdx % 2 === 0
            ? { color: 'var(--text-color)' }
            : { color: enhancedWordColor };

          return (
            <div
              key={wordIdx}
              className="flex flex-col items-center text-center hover:scale-105 transition-all duration-200 px-1 py-1.5 rounded-lg hover:bg-[var(--text-color)]/[0.07] min-w-[60px]"
              style={wordStyle}
            >
              <div className="text-3xl font-arabic leading-10 mb-1">{word.arabic}</div>
              <div className="text-xs font-sans leading-tight">{word.meaning}</div>
            </div>
          );
        })}

        {verse.sajdah && (
          <span className="text-amber-500 mx-2 self-center" title="Secde Ayeti">۩</span>
        )}

        <div className="inline-flex items-center self-center" style={{ marginLeft: '3rem' }}>
          <span
            className="inline-flex items-center justify-center relative px-2 py-1.5 rounded-lg text-sm mr-1"
            style={{
              color: 'var(--text-color)',
              backgroundColor: verseNumberBg
            }}
          >
            <span>{verse.verse_no}</span>
          </span>

          {onVerseMenuClick && (
            <button
              data-verse-menu-button={verseKey}
              onClick={(e) => {
                e.stopPropagation();
                onVerseMenuClick(verseKey, e);
              }}
              className="p-0.5 rounded-full hover:bg-[var(--text-color)]/10 transition-colors duration-150"
              style={{ color: 'var(--text-color)' }}
            >
              <MoreVertical size={14} />
            </button>
          )}
        </div>
      </div>
      
      <TranslationDisplay 
        verse={verse}
        selectedTranslators={selectedTranslators}
        availableTranslators={availableTranslators}
        sectionLeftBorderColor={sectionLeftBorderColor}
        translatorTitleColor={translatorTitleColor}
        footnoteColor={footnoteColor}
        displayMode={'3'}
      />
    </div>
  );
}; 