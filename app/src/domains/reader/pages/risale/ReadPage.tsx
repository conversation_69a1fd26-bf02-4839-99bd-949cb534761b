import { useState, useRef } from 'react';
import * as React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { PageLayout, EnhancedErrorState, LoadingState, Tooltip } from '@shared/components';
import { useRisaleSection, useRisaleProcess, useRisaleNavigationSheet, useReaderInteractivity } from '@reader/hooks'; // imports from index file
import { RisaleSectionDef } from '@reader/models/types';
import { ChevronDown } from 'lucide-react';
import NavigationSheet from './Components/NavigationSheet';
import { useIsMobile } from '@shared/hooks/useIsMobile';

import RisaleHeader from './Components/RisaleHeader';
import RisaleContent from './Components/RisaleContent';
import RisaleFootnotes from './Components/RisaleFootnotes';
import RisaleDictionary from './Components/RisaleDictionary';
import RisaleNavigation from './Components/RisaleNavigation';

const ReadPage = () => {
  const { bookId, sectionId } = useParams<{ bookId: string; sectionId: string }>();
  const navigate = useNavigate();
  const [isFootnotesOpen, setIsFootnotesOpen] = useState(false);
  const [isDictionaryOpen, setIsDictionaryOpen] = useState(false);
  const isMobile = useIsMobile();
  
  const contentRef = useRef<HTMLDivElement>(null);
  const actionMenuRef = useRef<HTMLDivElement>(null);

  const { data: sectionData, loading: isLoadingContent, error } = useRisaleSection(bookId, sectionId);
  
  const {
    isOpen,
    setIsOpen,
    navigationType,
    setNavigationType,
    sectionSearch,
    setSectionSearch,
    bookSearch,
    setBookSearch,
    handleNavigation: handleNavSheetNavigation,
    switchBookInSheet,
    selectedBookId,
    filteredSections,
    openNavigationSheet,
    isLoadingSections,
    bookTitle
  } = useRisaleNavigationSheet({ 
    currentBookId: bookId,
    initialBookTitle: sectionData?.metadata?.title,
  });

  const {
    isTooltipVisible,
    tooltipPosition,
    tooltipContent,
    hideTooltip,
  } = useReaderInteractivity({
    contentRef,
    actionMenuRef,
    sectionData,
  });

  const { processedSentences } = useRisaleProcess(sectionData);

  // Debug log removed for production

  if (isLoadingContent) return <LoadingState message="İçerik yükleniyor..." />;
  if (error) return <EnhancedErrorState error={error} onRetry={() => window.location.reload()} />;
  if (!sectionData || !sectionData.structure) return <LoadingState message="Bölüm bilgisi bulunamadı..." />;

  const { structure, content, title: sectionTitle, metadata } = sectionData;

  const breadcrumbContent = (
    <div className="w-full overflow-x-auto whitespace-nowrap text-center py-1">
      <div className="inline-flex items-center justify-center min-w-max">
        <button
          onClick={() => openNavigationSheet('books')}
          className="flex items-center gap-0.5 px-2 py-1 rounded-lg"
          title={metadata?.title || 'Kitap Seç'}
        >
          <span className="text-sm font-medium">{metadata?.title || 'Kitap'}</span>
          <ChevronDown size={14} className="opacity-80" />
        </button>
        <span className="mx-1 text-sm opacity-50">/</span>
        <button
          onClick={() => openNavigationSheet('section')}
          className="flex items-center gap-0.5 px-2 py-1 rounded-lg"
          title={sectionTitle || 'Bölüm Seç'}
        >
          <span className="text-sm font-medium">{sectionTitle || 'Bölüm'}</span>
          <ChevronDown size={14} className="ml-1 opacity-80" />
        </button>
      </div>
    </div>
  );

  return (
    <PageLayout
      navbarCenterContent={isMobile ? null : breadcrumbContent}
      secondRowContent={isMobile ? breadcrumbContent : null}
      navbarTwoRows={isMobile}
    >
      <NavigationSheet
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        navigationType={navigationType}
        onChangeNavigationType={setNavigationType}
        bookId={selectedBookId || bookId || ''}
        bookTitle={bookTitle || ''}
        sectionSearch={sectionSearch}
        onChangeSectionSearch={setSectionSearch}
        bookSearch={bookSearch}
        onChangeBookSearch={setBookSearch}
        filteredSections={filteredSections}
        onSelectSection={(sectionId) => handleNavSheetNavigation(sectionId)}
        onSelectBook={(selectedBookId: number) => {
          setIsOpen(false);
          navigate(`/risale/${selectedBookId}`);
        }}
        onSwitchBookInSheet={switchBookInSheet}
        isLoadingSections={isLoadingSections}
      />
      <main className="risale-read-page max-w-3xl mx-auto px-4 py-8 space-y-6">
        <RisaleHeader title={sectionTitle} />
        <RisaleContent ref={contentRef} sentences={processedSentences} />
        <RisaleFootnotes
          footnotes={content.footnotes || []}
          isOpen={isFootnotesOpen}
          setIsOpen={setIsFootnotesOpen}
        />
        <RisaleDictionary
          dictionary={content.dictionary || []}
          isOpen={isDictionaryOpen}
          setIsOpen={setIsDictionaryOpen}
        />
        <RisaleNavigation
          prevSection={structure.prevSection as RisaleSectionDef || null}
          nextSection={structure.nextSection as RisaleSectionDef || null}
          navigateToSection={(id) => {
              if (bookId) navigate(`/risale/${bookId}/${id}`);
          }}
        />
      </main>
      {isTooltipVisible && tooltipPosition && (
        <Tooltip
          content={tooltipContent}
          position={tooltipPosition}
          isVisible={isTooltipVisible}
          onClose={hideTooltip}
        />
      )}
    </PageLayout>
  );
};

export default ReadPage;
