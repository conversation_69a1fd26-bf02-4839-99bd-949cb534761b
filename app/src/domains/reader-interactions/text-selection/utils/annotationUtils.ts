/**
 * Annotation Utilities
 * Annotation işlemleri için yardımcı fonksiyonlar
 * Birleştirilmiş dosya: recoveryUtils.ts + diğer annotation yardımcıları
 */

// ✅ YENİ ORGANİZE YAPISI - Shared types
import type { Annotation } from '../../shared/types';
import { extractWords, compareHashes, createTextHash } from './textUtils';

// ============================================================================
// ANNOTATION RECOVERY FUNCTIONS (eski recoveryUtils.ts)
// ============================================================================

/**
 * Annotation recovery confidence score'u hesaplar
 */
export interface RecoveryResult {
  isFound: boolean;
  confidence: number;
  position?: { start: number; end: number };
  method: 'exact' | 'hash' | 'proximity' | 'fuzzy' | 'failed';
}

/**
 * Annotation'ı metinde bulmaya çalışır
 */
export async function findAnnotationInText(
  annotation: Annotation,
  currentText: string
): Promise<RecoveryResult> {
  // 1. Exact match dene
  const exactResult = findExactMatch(annotation, currentText);
  if (exactResult.isFound) {
    return exactResult;
  }

  // 2. Hash-based recovery dene
  const hashResult = await findByHash(annotation, currentText);
  if (hashResult.isFound) {
    return hashResult;
  }

  // 3. Word proximity dene
  const proximityResult = findByWordProximity(annotation, currentText);
  if (proximityResult.isFound) {
    return proximityResult;
  }

  // 4. Fuzzy matching dene
  const fuzzyResult = findByFuzzyMatch(annotation, currentText);
  if (fuzzyResult.isFound) {
    return fuzzyResult;
  }

  return {
    isFound: false,
    confidence: 0,
    method: 'failed'
  };
}

/**
 * Exact text match
 */
function findExactMatch(annotation: Annotation, currentText: string): RecoveryResult {
  const index = currentText.indexOf(annotation.selected_text);
  
  if (index !== -1) {
    return {
      isFound: true,
      confidence: 1.0,
      position: {
        start: index,
        end: index + annotation.selected_text.length
      },
      method: 'exact'
    };
  }

  return { isFound: false, confidence: 0, method: 'exact' };
}

/**
 * Hash-based recovery
 */
async function findByHash(annotation: Annotation, currentText: string): Promise<RecoveryResult> {
  // Prefix ve suffix kullanarak pozisyon tahmin et
  const prefixIndex = currentText.indexOf(annotation.prefix_text);
  const suffixIndex = currentText.indexOf(annotation.suffix_text);

  if (prefixIndex === -1 || suffixIndex === -1) {
    return { isFound: false, confidence: 0, method: 'hash' };
  }

  const estimatedStart = prefixIndex + annotation.prefix_text.length;
  const estimatedEnd = suffixIndex;

  if (estimatedStart >= estimatedEnd) {
    return { isFound: false, confidence: 0, method: 'hash' };
  }

  const candidateText = currentText.substring(estimatedStart, estimatedEnd);
  const candidateHash = await createTextHash(candidateText);

  if (compareHashes(candidateHash, annotation.text_hash)) {
    return {
      isFound: true,
      confidence: 0.9,
      position: { start: estimatedStart, end: estimatedEnd },
      method: 'hash'
    };
  }

  return { isFound: false, confidence: 0, method: 'hash' };
}

/**
 * Word proximity-based recovery
 */
function findByWordProximity(annotation: Annotation, currentText: string): RecoveryResult {
  const textWords = extractWords(currentText);
  const proximityWords = annotation.word_proximity;

  // Proximity kelimelerini metinde bul
  const wordPositions: number[] = [];
  
  proximityWords.forEach(word => {
    const index = textWords.indexOf(word);
    if (index !== -1) {
      wordPositions.push(index);
    }
  });

  if (wordPositions.length < 2) {
    return { isFound: false, confidence: 0, method: 'proximity' };
  }

  // En yakın kelimeler arasındaki alanı tahmin et
  const minPos = Math.min(...wordPositions);
  const maxPos = Math.max(...wordPositions);
  
  // Bu basit bir implementasyon - geliştirilmesi gerekiyor
  const confidence = wordPositions.length / proximityWords.length;
  
  if (confidence > 0.5) {
    return {
      isFound: true,
      confidence: confidence * 0.7, // Proximity güvenilirliği daha düşük
      position: { start: minPos * 10, end: maxPos * 10 }, // Rough estimate
      method: 'proximity'
    };
  }

  return { isFound: false, confidence: 0, method: 'proximity' };
}

/**
 * Fuzzy matching
 */
function findByFuzzyMatch(annotation: Annotation, currentText: string): RecoveryResult {
  const selectedText = annotation.selected_text;
  const threshold = 0.8; // %80 benzerlik
  
  // Sliding window ile benzer metinleri ara
  for (let i = 0; i <= currentText.length - selectedText.length; i++) {
    const candidate = currentText.substring(i, i + selectedText.length);
    const similarity = calculateSimilarity(selectedText, candidate);
    
    if (similarity >= threshold) {
      return {
        isFound: true,
        confidence: similarity * 0.6, // Fuzzy güvenilirliği daha düşük
        position: { start: i, end: i + selectedText.length },
        method: 'fuzzy'
      };
    }
  }

  return { isFound: false, confidence: 0, method: 'fuzzy' };
}

/**
 * İki metin arasındaki benzerliği hesaplar (Levenshtein distance)
 */
function calculateSimilarity(text1: string, text2: string): number {
  const matrix: number[][] = [];
  const len1 = text1.length;
  const len2 = text2.length;

  // Matrix'i başlat
  for (let i = 0; i <= len1; i++) {
    matrix[i] = [i];
  }
  for (let j = 0; j <= len2; j++) {
    matrix[0][j] = j;
  }

  // Levenshtein distance hesapla
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = text1[i - 1] === text2[j - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,     // deletion
        matrix[i][j - 1] + 1,     // insertion
        matrix[i - 1][j - 1] + cost // substitution
      );
    }
  }

  const distance = matrix[len1][len2];
  const maxLength = Math.max(len1, len2);
  return maxLength === 0 ? 1 : (maxLength - distance) / maxLength;
}

// ============================================================================
// ANNOTATION VALIDATION FUNCTIONS
// ============================================================================

/**
 * Annotation data'sını validate eder
 */
export function validateAnnotationData(data: Record<string, unknown>): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!data.selected_text || typeof data.selected_text !== 'string') {
    errors.push('Selected text is required');
  }

  if (!data.annotation_type || !['note', 'sherh', 'highlight', 'bookmark'].includes(data.annotation_type as string)) {
    errors.push('Valid annotation type is required');
  }

  if ((data.annotation_type === 'note' || data.annotation_type === 'sherh') && (!data.annotation_content || !(data.annotation_content as string).trim())) {
    errors.push('Content is required for note and sherh annotations');
  }

  if (typeof data.selection_start !== 'number' || data.selection_start < 0) {
    errors.push('Valid selection start position is required');
  }

  if (typeof data.selection_end !== 'number' || data.selection_end <= (data.selection_start as number)) {
    errors.push('Valid selection end position is required');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Annotation color'ını validate eder
 */
export function validateAnnotationColor(color: string): boolean {
  const validColors = ['#fbbf24', '#10b981', '#3b82f6', '#8b5cf6', '#ec4899', '#f97316'];
  return validColors.includes(color);
}

/**
 * Annotation tags'lerini validate eder
 */
export function validateAnnotationTags(tags: string[]): { isValid: boolean; cleanedTags: string[] } {
  const cleanedTags = tags
    .filter(tag => typeof tag === 'string' && tag.trim().length > 0)
    .map(tag => tag.trim().toLowerCase())
    .slice(0, 10); // Maksimum 10 tag

  return {
    isValid: true,
    cleanedTags
  };
}
