import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuthStore } from '@domains/auth/store/authStore';
import { annotationService } from '../services/annotationService';
import { findAnnotationInText } from '../../text-selection/utils/annotationUtils';
// ✅ YENİ ORGANİZE YAPISI - Shared types
import type {
  Annotation,
  CreateAnnotationInput,
  UpdateAnnotationInput,
  AnnotationFilters,
  AnnotationStats,
  AnnotationPosition
} from '../../shared/types';

/**
 * useAnnotationManager Hook
 * Annotation CRUD işlemleri ve state yönetimi
 */
export function useAnnotationManager(filters?: AnnotationFilters) {
  const { user } = useAuthStore();
  const [annotations, setAnnotations] = useState<Annotation[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<AnnotationStats | null>(null);

  // Stable filters dependency
  const stableFilters = useMemo(() =>
    JSON.stringify(filters || {}),
    [filters]
  );

  /**
   * Annotation'ları yükler
   */
  const loadAnnotations = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const response = await annotationService.getAnnotations({
        ...filters,
        user_id: user.id
      });

      if (response.error) {
        setError(response.error.message);
        return;
      }

      setAnnotations(response.data || []);
    } catch (err) {
      console.error('[useAnnotations] Load error:', err);
      setError('Annotation\'lar yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  }, [user, stableFilters]); // Sadece user ve stableFilters

  /**
   * Yeni annotation oluşturur
   */
  const createAnnotation = useCallback(async (input: CreateAnnotationInput): Promise<Annotation | null> => {
    if (!user) {
      setError('Giriş yapmanız gerekiyor');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await annotationService.createAnnotation(input);

      if (response.error) {
        setError(response.error.message);
        return null;
      }

      const newAnnotation = response.data!;
      setAnnotations(prev => [newAnnotation, ...prev]);

      return newAnnotation;
    } catch (err) {
      console.error('[useAnnotations] Create error:', err);
      setError('Annotation oluşturulurken hata oluştu');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  /**
   * Annotation'ları getirir (wrapper for annotation service)
   */
  const getAnnotations = useCallback(async (filters?: AnnotationFilters): Promise<Annotation[]> => {
    try {
      const response = await annotationService.getAnnotations(filters);
      
      if (response.error) {
        console.error('[useAnnotationManager] getAnnotations error:', response.error);
        return [];
      }

      return response.data || [];
    } catch (err) {
      console.error('[useAnnotationManager] getAnnotations error:', err);
      return [];
    }
  }, []);

  /**
   * Annotation'ı günceller
   */
  const updateAnnotation = useCallback(async (
    id: string,
    input: UpdateAnnotationInput
  ): Promise<Annotation | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await annotationService.updateAnnotation(id, input);

      if (response.error) {
        setError(response.error.message);
        return null;
      }

      const updatedAnnotation = response.data!;
      setAnnotations(prev =>
        prev.map(annotation =>
          annotation.id === id ? updatedAnnotation : annotation
        )
      );

      return updatedAnnotation;
    } catch (err) {
      console.error('[useAnnotations] Update error:', err);
      setError('Annotation güncellenirken hata oluştu');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Annotation'ı siler
   */
  const deleteAnnotation = useCallback(async (id: string): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await annotationService.deleteAnnotation(id);

      if (response.error) {
        setError(response.error.message);
        return false;
      }

      setAnnotations(prev => prev.filter(annotation => annotation.id !== id));
      return true;
    } catch (err) {
      console.error('[useAnnotations] Delete error:', err);
      setError('Annotation silinirken hata oluştu');
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Seçilen metinle eşleşen annotation'ları akıllı şekilde siler/böler
   */
  const smartDeleteAnnotationsBySelection = useCallback(async (
    bookId: string,
    sectionId: string,
    selectedText: string
  ): Promise<{ deletedCount: number; modifiedCount: number }> => {
    if (!user) {
      setError('Giriş yapmanız gerekiyor');
      return { deletedCount: 0, modifiedCount: 0 };
    }

    setLoading(true);
    setError(null);

    try {
      const response = await annotationService.smartDeleteAnnotationsBySelection(
        bookId,
        sectionId,
        selectedText,
        user.id
      );

      if (response.error) {
        setError(response.error.message);
        return { deletedCount: 0, modifiedCount: 0 };
      }

      const result = response.data || { deletedCount: 0, modifiedCount: 0 };

      // Local state'i güncelle - annotation'ları yeniden yükle
      if (result.deletedCount > 0 || result.modifiedCount > 0) {
        // Akıllı silme/bölme işlemi yapıldı, annotation'ları yeniden yükle
        await loadAnnotations();
      }

      return result;
    } catch (err) {
      console.error('[useAnnotations] Smart delete by selection error:', err);
      setError('Annotation\'lar işlenirken hata oluştu');
      return { deletedCount: 0, modifiedCount: 0 };
    } finally {
      setLoading(false);
    }
  }, [user, loadAnnotations]);



  /**
   * Seçilen metinle eşleşen annotation'ları siler (basit versiyon)
   */
  const deleteAnnotationsBySelection = useCallback(async (
    bookId: string,
    sectionId: string,
    selectedText: string
  ): Promise<number> => {
    if (!user) {
      setError('Giriş yapmanız gerekiyor');
      return 0;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await annotationService.deleteAnnotationsBySelection(
        bookId,
        sectionId,
        selectedText,
        user.id
      );

      if (response.error) {
        setError(response.error.message);
        return 0;
      }

      const deletedCount = response.data || 0;

      // Local state'i güncelle - silinen annotation'ları kaldır
      if (deletedCount > 0) {
        setAnnotations(prev =>
          prev.filter(annotation =>
            !(annotation.book_id === bookId &&
              annotation.section_id === sectionId &&
              annotation.annotation_type === 'highlight' &&
              (annotation.selected_text === selectedText ||
               annotation.selected_text.includes(selectedText) ||
               selectedText.includes(annotation.selected_text)))
          )
        );
      }

      return deletedCount;
    } catch (err) {
      console.error('[useAnnotations] Delete by selection error:', err);
      setError('Annotation\'lar silinirken hata oluştu');
      return 0;
    } finally {
      setLoading(false);
    }
  }, [user]);

  /**
   * İstatistikleri yükler
   */
  const loadStats = useCallback(async () => {
    if (!user) return;

    try {
      const response = await annotationService.getAnnotationStats(user.id);

      if (response.error) {
        console.error('[useAnnotations] Stats error:', response.error);
        return;
      }

      setStats(response.data);
    } catch (err) {
      console.error('[useAnnotations] Stats load error:', err);
    }
  }, [user]);

  /**
   * Annotation pozisyonunu recover eder
   */
  const recoverPosition = useCallback(async (
    annotation: Annotation,
    currentText: string
  ): Promise<AnnotationPosition | null> => {
    try {
      const result = await findAnnotationInText(annotation, currentText);
      if (result.isFound && result.position) {
        return {
          start: result.position.start,
          end: result.position.end,
          confidence: result.confidence
        };
      }
      return null;
    } catch (err) {
      console.error('[useAnnotations] Recovery error:', err);
      return null;
    }
  }, []);

  /**
   * Annotation'ı ID ile bulur
   */
  const findAnnotationById = useCallback((id: string): Annotation | undefined => {
    return annotations.find(annotation => annotation.id === id);
  }, [annotations]);

  /**
   * Belirli bir sentence'daki annotation'ları filtreler
   */
  const getAnnotationsBySentence = useCallback((sentenceId: string): Annotation[] => {
    return annotations.filter(annotation => annotation.sentence_id === sentenceId);
  }, [annotations]);

  /**
   * Belirli bir tip annotation'ları filtreler
   */
  const getAnnotationsByType = useCallback((type: string): Annotation[] => {
    return annotations.filter(annotation => annotation.annotation_type === type);
  }, [annotations]);

  /**
   * Tag'e göre annotation'ları filtreler
   */
  const getAnnotationsByTag = useCallback((tag: string): Annotation[] => {
    return annotations.filter(annotation => annotation.tags.includes(tag));
  }, [annotations]);

  /**
   * Tüm kullanılan tag'leri getirir
   */
  const getAllTags = useCallback((): string[] => {
    const allTags = annotations.flatMap(annotation => annotation.tags);
    return [...new Set(allTags)].sort();
  }, [annotations]);

  /**
   * Annotation'ları temizler
   */
  const clearAnnotations = useCallback(() => {
    setAnnotations([]);
    setStats(null);
    setError(null);
  }, []);

  /**
   * Error'ı temizler
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // İlk yükleme
  useEffect(() => {
    if (user) {
      loadAnnotations();
      loadStats();
    } else {
      clearAnnotations();
    }
  }, [user, stableFilters]); // Sadece user ve stableFilters - fonksiyon dependency'lerini kaldırdık

  return {
    // State
    annotations,
    loading,
    error,
    stats,

    // Actions
    loadAnnotations,
    createAnnotation,
    updateAnnotation,
    deleteAnnotation,
    deleteAnnotationsBySelection,
    smartDeleteAnnotationsBySelection,
    loadStats,
    recoverPosition,

    // Helpers
    findAnnotationById,
    getAnnotationsBySentence,
    getAnnotationsByType,
    getAnnotationsByTag,
    getAllTags,
    clearAnnotations,
    clearError,

    // Computed
    totalCount: annotations.length,
    hasAnnotations: annotations.length > 0,
    getAnnotations // Add this line
  };
}
