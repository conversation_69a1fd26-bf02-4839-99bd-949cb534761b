import { useState, useEffect, useMemo } from 'react';
import { MessageSquare, Highlighter, Bookmark, Eye, EyeOff, Globe, ChevronUp, ChevronDown } from 'lucide-react';
// ✅ YENİ ORGANİZE YAPISI - Feature-based imports
import { useReaderInteractionStyles } from '../hooks/useReaderInteractionStyles';
import { useAnnotationManager } from '../../annotations/hooks/useAnnotationManager';
import { useAuthStore } from '@domains/auth/store/authStore';
import { useParams, useNavigate } from 'react-router-dom';
import { useIsMobile } from '@shared/hooks/useIsMobile';
import { useEnhancedColorHue } from '@shared/color-system';
import type { Annotation } from '../types';

interface AnnotationFloatingPanelProps {
  className?: string;
  showNotes?: boolean;
  setShowNotes?: (show: boolean) => void;
  showHighlights?: boolean;
  setShowHighlights?: (show: boolean) => void;
  showBookmarks?: boolean;
  setShowBookmarks?: (show: boolean) => void;
}

/**
 * Floating Annotation Panel
 * Sağ alt köşede kullanıcının annotation'larını gösteren minimal panel
 */
export function AnnotationFloatingPanel({
  className = '',
  showNotes: externalShowNotes,
  setShowNotes: externalSetShowNotes,
  showHighlights: externalShowHighlights,
  setShowHighlights: externalSetShowHighlights,
  showBookmarks: externalShowBookmarks,
  setShowBookmarks: externalSetShowBookmarks
}: AnnotationFloatingPanelProps) {
  const { bookId, sectionId } = useParams<{ bookId: string; sectionId: string }>();
  const navigate = useNavigate();
  const user = useAuthStore((state) => state.user);
  const isMobile = useIsMobile();
  const [isExpanded, setIsExpanded] = useState(false);
  const [isVisible, setIsVisible] = useState(true);

  // Use external state if provided, otherwise use internal state
  const [internalShowNotes, setInternalShowNotes] = useState(true);
  const [internalShowHighlights, setInternalShowHighlights] = useState(true);
  const [internalShowBookmarks, setInternalShowBookmarks] = useState(true);

  const showNotes = externalShowNotes ?? internalShowNotes;
  const setShowNotes = externalSetShowNotes ?? setInternalShowNotes;
  const showHighlights = externalShowHighlights ?? internalShowHighlights;
  const setShowHighlights = externalSetShowHighlights ?? setInternalShowHighlights;
  const showBookmarks = externalShowBookmarks ?? internalShowBookmarks;
  const setShowBookmarks = externalSetShowBookmarks ?? setInternalShowBookmarks;

  // Enhanced color for footer button
  const enhancedButtonColor = useEnhancedColorHue('var(--text-color)');

  // Bölümlerin açık/kapalı durumu için ayrı state'ler
  const [isNotesExpanded, setIsNotesExpanded] = useState(true);
  const [isHighlightsExpanded, setIsHighlightsExpanded] = useState(true);
  const [isBookmarksExpanded, setIsBookmarksExpanded] = useState(true);

  // Styles
  const { bgColors, borderColors } = useReaderInteractionStyles();

  // Annotation'ları yükle
  const { annotations, loadAnnotations } = useAnnotationManager({
    book_id: bookId,
    section_id: sectionId,
    user_id: user?.id
  });

  // Sayfa değiştiğinde annotation'ları yükle
  useEffect(() => {
    if (user && bookId && sectionId) {
      loadAnnotations();
    }
  }, [user, bookId, sectionId]); // loadAnnotations dependency'sini kaldırdık

  // Annotation'ları tiplere göre filtrele (memoized)
  const { noteAnnotations, highlightAnnotations, bookmarkAnnotations } = useMemo(() => {
    return {
      noteAnnotations: annotations.filter(a => a.annotation_type === 'note'),
      highlightAnnotations: annotations.filter(a => a.annotation_type === 'highlight'),
      bookmarkAnnotations: annotations.filter(a => a.annotation_type === 'bookmark')
    };
  }, [annotations]);

  // Panel her zaman görünsün (üye olmasa da, annotation olmasa da)

  // Annotation'a tıklama handler'ı
  const handleAnnotationClick = (annotation: Annotation) => {
    if (!user) {
      alert('Bu özelliği kullanmak için üye olmanız gerekiyor.');
      return;
    }

    // Annotation'ın bulunduğu sentence'a scroll yap
    const sentenceElement = document.getElementById(`sentence-${annotation.sentence_id}`);
    if (sentenceElement) {
      sentenceElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });

      // Kısa bir highlight efekti
      sentenceElement.style.transition = 'background-color 0.3s ease';
      sentenceElement.style.backgroundColor = 'rgba(59, 130, 246, 0.1)';
      setTimeout(() => {
        sentenceElement.style.backgroundColor = 'transparent';
      }, 1000);
    }
  };

  // Panel tıklama handler'ı (üye olmayanlara uyarı)
  const handlePanelClick = () => {
    if (!user) {
      alert('Şerh ve vurgulama özelliklerini kullanmak için üye olmanız gerekiyor.');
      return;
    }
  };

  // Annotation preview text'i (ilk 30 karakter)
  const getPreviewText = (annotation: Annotation) => {
    const text = annotation.selected_text || annotation.annotation_content || '';
    return text.length > 30 ? text.substring(0, 30) + '...' : text;
  };

  // Responsive boyutlar
  const panelSizes = {
    collapsed: {
      width: isMobile ? '160px' : '200px'
    },
    expanded: {
      width: isMobile ? '300px' : '350px',
      maxHeight: isMobile ? '90vh' : '85vh' // Neredeyse tam ekran
    }
  };

  return (
    <div
      className={`fixed bottom-4 right-4 z-40 transition-all duration-300 ${className}`}
      style={{
        maxWidth: isExpanded ? panelSizes.expanded.width : panelSizes.collapsed.width,
        minWidth: isExpanded ? panelSizes.expanded.width : panelSizes.collapsed.width
      }}
    >
      {/* Minimal Panel */}
      {!isExpanded && (
        <div
          className="rounded-lg shadow-lg border cursor-pointer hover:shadow-xl transition-all duration-200"
          style={{
            backgroundColor: bgColors.card,
            borderColor: borderColors.default
          }}
          onClick={() => {
            if (!user) {
              handlePanelClick();
              return;
            }
            setIsExpanded(true);
          }}
        >
          {/* Header */}
          <div className="p-3 border-b" style={{ borderColor: borderColors.light }}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {/* Şerhler */}
                <div className="flex items-center space-x-1">
                  <MessageSquare size={16} style={{ color: '#fbbf24' }} />
                  <span className="text-sm font-medium" style={{ color: 'var(--text-color)' }}>
                    {noteAnnotations.length}
                  </span>
                </div>

                {/* Vurgular */}
                <div className="flex items-center space-x-1">
                  <Highlighter size={16} style={{ color: '#8b5cf6' }} />
                  <span className="text-sm font-medium" style={{ color: 'var(--text-color)' }}>
                    {highlightAnnotations.length}
                  </span>
                </div>

                {/* Kaydedilenler */}
                <div className="flex items-center space-x-1">
                  <Bookmark size={16} style={{ color: '#10b981' }} />
                  <span className="text-sm font-medium" style={{ color: 'var(--text-color)' }}>
                    {bookmarkAnnotations.length}
                  </span>
                </div>
              </div>

              <ChevronUp size={16} style={{ color: 'var(--text-color)', opacity: 0.6 }} />
            </div>
          </div>

          {/* Quick Preview */}
          <div className="p-2">
            <div className="text-xs opacity-75" style={{ color: 'var(--text-color)' }}>
              Detayları görmek için tıklayın
            </div>
          </div>
        </div>
      )}

      {/* Expanded Panel */}
      {isExpanded && (
        <div
          className="rounded-lg shadow-xl border overflow-hidden flex flex-col"
          style={{
            backgroundColor: bgColors.card,
            borderColor: borderColors.default,
            maxHeight: panelSizes.expanded.maxHeight
          }}
        >
          {/* Header */}
          <div className="p-3 border-b flex items-center justify-between" style={{ borderColor: borderColors.light }}>
            <h3 className="font-semibold text-sm" style={{ color: 'var(--text-color)' }}>
              Bu Sayfadaki İçeriklerim
            </h3>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => {
                  const newVisibility = !isVisible;
                  setIsVisible(newVisibility);
                  setShowNotes(newVisibility);
                  setShowHighlights(newVisibility);
                  setShowBookmarks(newVisibility);
                }}
                className="p-1 rounded hover:bg-opacity-10 hover:bg-gray-500 transition-colors"
                title={isVisible ? 'Tümünü Gizle' : 'Tümünü Göster'}
                style={{ color: 'var(--text-color)' }}
              >
                {isVisible ? <EyeOff size={14} /> : <Eye size={14} />}
              </button>
              <button
                onClick={() => setIsExpanded(false)}
                className="p-1 rounded hover:bg-opacity-10 hover:bg-gray-500 transition-colors"
                title="Küçült"
              >
                <ChevronUp size={14} className="rotate-180" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {/* Şerhler */}
            <div className="p-3 border-b" style={{ borderColor: borderColors.light }}>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <MessageSquare size={14} style={{ color: '#fbbf24' }} />
                  <span className="text-sm font-medium" style={{ color: 'var(--text-color)' }}>
                    Şerhlerim ({noteAnnotations.length})
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => {
                      if (!user) {
                        handlePanelClick();
                        return;
                      }
                      setShowNotes(!showNotes);
                    }}
                    className="p-1 rounded hover:bg-opacity-10 hover:bg-gray-500 transition-colors"
                    title={showNotes ? 'Şerhleri Gizle' : 'Şerhleri Göster'}
                    style={{ color: 'var(--text-color)', opacity: 0.7 }}
                  >
                    {showNotes ? <EyeOff size={12} /> : <Eye size={12} />}
                  </button>
                  <button
                    onClick={() => {
                      setIsNotesExpanded(!isNotesExpanded);
                    }}
                    className="p-1 rounded hover:bg-opacity-10 hover:bg-gray-500 transition-colors"
                    title={isNotesExpanded ? 'Kapat' : 'Aç'}
                    style={{ color: 'var(--text-color)', opacity: 0.7 }}
                  >
                    {isNotesExpanded ? <ChevronUp size={12} /> : <ChevronDown size={12} />}
                  </button>
                </div>
              </div>
              {isNotesExpanded && noteAnnotations.length > 0 && (
                <div className="space-y-1">
                  {noteAnnotations.slice(0, 8).map((annotation) => (
                  <div
                    key={annotation.id}
                    className="text-xs p-2 rounded cursor-pointer hover:bg-opacity-5 hover:bg-yellow-500 transition-colors"
                    style={{ backgroundColor: bgColors.hover }}
                    onClick={() => handleAnnotationClick(annotation)}
                  >
                    <div className="font-medium mb-1" style={{ color: 'var(--text-color)' }}>
                      "{getPreviewText(annotation)}"
                    </div>
                    {annotation.annotation_content && (
                      <div className="opacity-75" style={{ color: 'var(--text-color)' }}>
                        {annotation.annotation_content.length > 40
                          ? annotation.annotation_content.substring(0, 40) + '...'
                          : annotation.annotation_content
                        }
                      </div>
                    )}
                  </div>
                ))}
                  {noteAnnotations.length > 8 && (
                    <div className="text-xs opacity-75 text-center py-1" style={{ color: 'var(--text-color)' }}>
                      +{noteAnnotations.length - 8} daha...
                    </div>
                  )}
                </div>
              )}
              {isNotesExpanded && noteAnnotations.length === 0 && (
                <div className="text-xs opacity-50 text-center py-2" style={{ color: 'var(--text-color)' }}>
                  Henüz şerh eklenmemiş
                </div>
              )}
            </div>

            {/* Vurgular */}
            <div className="p-3 border-b" style={{ borderColor: borderColors.light }}>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Highlighter size={14} style={{ color: '#8b5cf6' }} />
                  <span className="text-sm font-medium" style={{ color: 'var(--text-color)' }}>
                    Vurgularım ({highlightAnnotations.length})
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => {
                      if (!user) {
                        handlePanelClick();
                        return;
                      }
                      setShowHighlights(!showHighlights);
                    }}
                    className="p-1 rounded hover:bg-opacity-10 hover:bg-gray-500 transition-colors"
                    title={showHighlights ? 'Vurguları Gizle' : 'Vurguları Göster'}
                    style={{ color: 'var(--text-color)', opacity: 0.7 }}
                  >
                    {showHighlights ? <EyeOff size={12} /> : <Eye size={12} />}
                  </button>
                  <button
                    onClick={() => {
                      setIsHighlightsExpanded(!isHighlightsExpanded);
                    }}
                    className="p-1 rounded hover:bg-opacity-10 hover:bg-gray-500 transition-colors"
                    title={isHighlightsExpanded ? 'Kapat' : 'Aç'}
                    style={{ color: 'var(--text-color)', opacity: 0.7 }}
                  >
                    {isHighlightsExpanded ? <ChevronUp size={12} /> : <ChevronDown size={12} />}
                  </button>
                </div>
              </div>
              {isHighlightsExpanded && highlightAnnotations.length > 0 && (
                <div className="space-y-1">
                  {highlightAnnotations.slice(0, 8).map((annotation) => (
                  <div
                    key={annotation.id}
                    className="text-xs p-2 rounded cursor-pointer hover:bg-opacity-5 hover:bg-purple-500 transition-colors"
                    style={{
                      backgroundColor: annotation.color ? `${annotation.color}20` : bgColors.hover,
                      borderLeft: `3px solid ${annotation.color || '#8b5cf6'}`
                    }}
                    onClick={() => handleAnnotationClick(annotation)}
                  >
                    <div style={{ color: 'var(--text-color)' }}>
                      "{getPreviewText(annotation)}"
                    </div>
                  </div>
                ))}
                  {highlightAnnotations.length > 8 && (
                    <div className="text-xs opacity-75 text-center py-1" style={{ color: 'var(--text-color)' }}>
                      +{highlightAnnotations.length - 8} daha...
                    </div>
                  )}
                </div>
              )}
              {isHighlightsExpanded && highlightAnnotations.length === 0 && (
                <div className="text-xs opacity-50 text-center py-2" style={{ color: 'var(--text-color)' }}>
                  Henüz vurgulama eklenmemiş
                </div>
              )}
            </div>

            {/* Kaydedilenler */}
            <div className="p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Bookmark size={14} style={{ color: '#10b981' }} />
                  <span className="text-sm font-medium" style={{ color: 'var(--text-color)' }}>
                    Kaydettiklerim ({bookmarkAnnotations.length})
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => {
                      if (!user) {
                        handlePanelClick();
                        return;
                      }
                      setShowBookmarks(!showBookmarks);
                    }}
                    className="p-1 rounded hover:bg-opacity-10 hover:bg-gray-500 transition-colors"
                    title={showBookmarks ? 'Kaydedilenleri Gizle' : 'Kaydedilenleri Göster'}
                    style={{ color: 'var(--text-color)', opacity: 0.7 }}
                  >
                    {showBookmarks ? <EyeOff size={12} /> : <Eye size={12} />}
                  </button>
                  <button
                    onClick={() => {
                      setIsBookmarksExpanded(!isBookmarksExpanded);
                    }}
                    className="p-1 rounded hover:bg-opacity-10 hover:bg-gray-500 transition-colors"
                    title={isBookmarksExpanded ? 'Kapat' : 'Aç'}
                    style={{ color: 'var(--text-color)', opacity: 0.7 }}
                  >
                    {isBookmarksExpanded ? <ChevronUp size={12} /> : <ChevronDown size={12} />}
                  </button>
                </div>
              </div>
              {isBookmarksExpanded && bookmarkAnnotations.length > 0 && (
                <div className="space-y-1">
                  {bookmarkAnnotations.slice(0, 8).map((annotation) => (
                  <div
                    key={annotation.id}
                    className="text-xs p-2 rounded cursor-pointer hover:bg-opacity-5 hover:bg-green-500 transition-colors"
                    style={{ backgroundColor: bgColors.hover }}
                    onClick={() => handleAnnotationClick(annotation)}
                  >
                    <div style={{ color: 'var(--text-color)' }}>
                      "{getPreviewText(annotation)}"
                    </div>
                  </div>
                ))}
                  {bookmarkAnnotations.length > 8 && (
                    <div className="text-xs opacity-75 text-center py-1" style={{ color: 'var(--text-color)' }}>
                      +{bookmarkAnnotations.length - 8} daha...
                    </div>
                  )}
                </div>
              )}
              {isBookmarksExpanded && bookmarkAnnotations.length === 0 && (
                <div className="text-xs opacity-50 text-center py-2" style={{ color: 'var(--text-color)' }}>
                  Henüz kayıt eklenmemiş
                </div>
              )}
            </div>
          </div>

          {/* Footer - Paylaşım Keşfi */}
          <div className="p-2 border-t" style={{ borderColor: borderColors.light }}>
            <button
              className="w-full text-xs p-2 rounded flex items-center justify-center space-x-1 hover:bg-opacity-5 hover:bg-blue-500 transition-colors"
              style={{
                color: enhancedButtonColor
              }}
              onClick={() => {
                if (!user) {
                  alert('Bu özelliği kullanmak için üye olmanız gerekiyor.');
                  return;
                }
                navigate('/saved-content');
              }}
            >
              <Globe size={12} />
              <span>Tüm içeriklerimi gör</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
