import { useState, useCallback, useEffect } from 'react';
import { useAnnotationManager } from '@domains/reader-interactions/annotations/hooks/useAnnotationManager';
import { useCollectionManager } from '@domains/reader-interactions/bookmarks/hooks/useCollectionManager';
import { useAuthStore } from '@domains/auth/store/authStore';
import { useReaderStore } from '@domains/reader/store/readerstore';
import {
  AnnotationService,
  SaveNoteInput,
  SaveBookmarkInput,
} from '@domains/reader-interactions/services/AnnotationService';
import { CreateCollectionInput } from '@domains/reader-interactions/shared/types/types';
import { CombinedVerseData, ITranslator } from '@reader/models/types';

interface SheetStates {
  note: {
    isOpen: boolean;
    content: string;
    id?: string; // For editing
    initialContent?: string; // For editing
  };
  notesList: {
    isOpen: boolean;
  };
  bookmark: {
    isOpen: boolean;
  };
  wordDetail: {
    isOpen: boolean;
  };
}

interface ToastState {
  isVisible: boolean;
  message: string;
  type: 'success' | 'error';
}

interface ActionContext {
  verseKey: string | null;
  verseData: CombinedVerseData | undefined;
  surahName?: string;
}

export const useReaderActions = (
  initialVerseKey: string | null,
  initialVerseData: CombinedVerseData | undefined,
  initialSurahName?: string,
  availableTranslators?: ITranslator[],
  selectedTranslators?: string[]
) => {
  const [actionContext, setActionContext] = useState<ActionContext>({
    verseKey: initialVerseKey,
    verseData: initialVerseData,
    surahName: initialSurahName,
  });

  const [sheetStates, setSheetStates] = useState<SheetStates>({
    note: { 
      isOpen: false, 
      content: '',
      id: undefined,
      initialContent: undefined
    },
    notesList: { isOpen: false },
    bookmark: { isOpen: false },
    wordDetail: { isOpen: false }
  });

  // Hook'un dış prop'larla senkronize olmasını sağlayan etki.
  // Bu, kullanıcı farklı bir ayet seçtiğinde context'in güncel kalmasını sağlar.
  useEffect(() => {
    // Eğer herhangi bir sheet açıksa, dışarıdan gelen güncellemeyi yoksay.
    // Bu, sheet'in kendi context'ini korumasını sağlar (race condition'ı önler).
    const anySheetOpen = Object.values(sheetStates).some(s => s.isOpen);
    if (anySheetOpen) {
      return;
    }

    setActionContext({
      verseKey: initialVerseKey,
      verseData: initialVerseData,
      surahName: initialSurahName,
    });
  }, [initialVerseKey, initialVerseData, initialSurahName, sheetStates]);

  const [toastState, setToastState] = useState<ToastState>({
    isVisible: false,
    message: '',
    type: 'success'
  });

  const { user } = useAuthStore();
  const { createAnnotation, getAnnotations, updateAnnotation } = useAnnotationManager();
  const { collections, loadCollections, createCollection } = useCollectionManager();
  const { openWordDetailSheet } = useReaderStore();

  // Initialize the service with the createAnnotation and getAnnotations functions
  useEffect(() => {
    AnnotationService.initialize(createAnnotation, getAnnotations);
  }, [createAnnotation, getAnnotations]);

  // Load collections when user changes
  useEffect(() => {
    if (user) {
      loadCollections();
    }
  }, [user, loadCollections]);

  // Toast functions
  const showToast = useCallback((message: string, type: 'success' | 'error' = 'success') => {
    setToastState({ isVisible: true, message, type });
  }, []);

  const hideToast = useCallback(() => {
    setToastState(prev => ({ ...prev, isVisible: false }));
  }, []);

  // Sheet control functions
  const openSheet = useCallback((type: keyof SheetStates) => {
    setSheetStates(prev => ({ ...prev, [type]: { ...prev[type], isOpen: true } }));
  }, []);

  const closeSheet = useCallback((type: keyof SheetStates) => {
    setSheetStates(prev => ({ ...prev, [type]: { ...prev[type], isOpen: false } }));
  }, []);

  // Action handlers
  const handleNoteSave = useCallback(async (content: string) => {
    const { verseKey, verseData, surahName } = actionContext;
    const { id: noteId } = sheetStates.note;

    if (!user || !verseData) {
      showToast('Kullanıcı veya ayet bilgileri eksik.', 'error');
      return;
    }

    // Eğer ID varsa, güncelleme yap
    if (noteId) {
      const result = await updateAnnotation(noteId, { annotation_content: content });
      if (result) {
        showToast('Not başarıyla güncellendi.', 'success');
        // Not listesini yenilemek için event veya callback düşünülebilir
      } else {
        showToast('Not güncellenirken hata oluştu.', 'error');
      }
    } 
    // ID yoksa, yeni not oluştur
    else if (verseKey) {
      const input: SaveNoteInput = {
        userId: user.id,
        verseKey,
        surahName,
        noteContent: content,
        verseData
      };
      const result = await AnnotationService.saveNote(input);
      if (result.success) {
        showToast(result.message, 'success');
      } else {
        showToast(result.message, 'error');
      }
    }

    closeSheet('note');
    // Artık buna gerek yok, çünkü content doğrudan parametre olarak geliyor
    // setNoteContent('');

  }, [user, actionContext, sheetStates.note, showToast, closeSheet, updateAnnotation]);

  const handleBookmarkSave = useCallback(async (collection: { id: string; name: string }, selectedMeals?: Array<{ id: string; enabled: boolean; name: string }>) => {
    const { verseKey, verseData, surahName } = actionContext;

    if (!user || !verseKey || !verseData) {
      showToast('Kullanıcı veya ayet bilgileri eksik.', 'error');
      return;
    }

    const input: SaveBookmarkInput = {
      userId: user.id,
      verseKey: verseKey,
      surahName: surahName,
      verseData: verseData,
      collection,
      selectedMeals,
      availableTranslators,
      selectedTranslators
    };

    const result = await AnnotationService.saveBookmark(input);

    if (result.success) {
      closeSheet('bookmark');
      showToast(result.message, 'success');
    } else {
      showToast(result.message, 'error');
    }
  }, [user, actionContext, availableTranslators, selectedTranslators, showToast, closeSheet]);

  const handleCreateCollection = useCallback(async (collectionInput: Omit<CreateCollectionInput, 'user_id'>) => {
    if (!user) return;
    try {
      await createCollection(collectionInput);
      await loadCollections();
    } catch (error) {
      console.error('Collection creation error:', error);
      throw error;
    }
  }, [user, createCollection, loadCollections]);

  const handleWordDetailClick = useCallback(() => {
    const { verseKey, verseData } = actionContext;
    if (!verseData || !verseKey) {
      showToast('Ayet bilgileri eksik.', 'error');
      return;
    }
    openWordDetailSheet(verseKey);
  }, [actionContext, openWordDetailSheet, showToast]);

  // User validation functions
  const validateUser = useCallback((actionName: string) => {
    if (!user) {
      showToast(`${actionName} için giriş yapmanız gerekiyor.`, 'error');
      return false;
    }
    return true;
  }, [user, showToast]);

  const validateVerseData = useCallback(() => {
    const { verseKey, verseData } = actionContext;
    if (!verseData || !verseKey) {
      showToast('Ayet bilgileri eksik.', 'error');
      return false;
    }
    return true;
  }, [actionContext, showToast]);

  // Main action handlers that accept parameters
  const handleNoteAction = useCallback((actionVerseKey: string, actionVerseData: CombinedVerseData, actionSurahName?: string) => {
    if (!validateUser('Not eklemek')) return;
    
    setActionContext({
      verseKey: actionVerseKey,
      verseData: actionVerseData,
      surahName: actionSurahName,
    });

    // Reset editing state when adding a new note
    setSheetStates(prev => ({
      ...prev,
      note: { isOpen: true, content: '', id: undefined, initialContent: undefined }
    }));
  }, [validateUser]);

  const handleNoteEditAction = useCallback((note: { id: string, content: string }) => {
    if (!validateUser('Not düzenlemek')) return;

    closeSheet('notesList');
    setSheetStates(prev => ({
      ...prev,
      note: {
        isOpen: true,
        content: note.content,
        id: note.id,
        initialContent: note.content
      }
    }));
  }, [validateUser, closeSheet]);

  const handleNotesListAction = useCallback((actionVerseKey: string, actionVerseData: CombinedVerseData, actionSurahName?: string) => {
    if (!validateUser('Notları görüntülemek')) return;
    
    setActionContext({
      verseKey: actionVerseKey,
      verseData: actionVerseData,
      surahName: actionSurahName,
    });

    openSheet('notesList');
  }, [validateUser, openSheet]);

  const handleBookmarkAction = useCallback((actionVerseKey: string, actionVerseData: CombinedVerseData, actionSurahName?: string) => {
    if (!validateUser('Yer imi eklemek')) return;

    setActionContext({
      verseKey: actionVerseKey,
      verseData: actionVerseData,
      surahName: actionSurahName,
    });
    
    openSheet('bookmark');
  }, [validateUser, openSheet]);

  const handleWordDetailAction = useCallback(() => {
    if (!validateVerseData()) return;
    handleWordDetailClick();
  }, [validateVerseData, handleWordDetailClick]);

  return {
    // States
    sheetStates,
    toastState,
    collections,
    actionContext,

    // Sheet controls
    openSheet,
    closeSheet,

    // Toast controls
    showToast,
    hideToast,

    // Action handlers
    handleNoteAction,
    handleNotesListAction,
    handleBookmarkAction,
    handleWordDetailAction,
    handleNoteSave,
    handleBookmarkSave,
    handleCreateCollection,
    handleNoteEditAction,

    // Validation helpers
    validateUser,
    validateVerseData
  };
};
