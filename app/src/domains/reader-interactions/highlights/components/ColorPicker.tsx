import React, { memo } from 'react';
import { Check, Trash2 } from 'lucide-react';
// ✅ YENİ ORGANİZE YAPISI - Shared hooks
import { useReaderInteractionStyles } from '../../shared/hooks/useReaderInteractionStyles';
// 🎨 Yeni renk sistemi
import { useColorPicker } from '@shared/color-system';

/**
 * @deprecated Legacy ColorPicker - Use AdaptiveColorPicker instead
 *
 * ⚠️ Bu component deprecated! Yeni projeler için AdaptiveColorPicker kullanın:
 *
 * ```tsx
 * // ❌ ESKİ YÖNTEM
 * import ColorPicker from '@domains/reader-interactions/highlights/components/ColorPicker';
 *
 * // ✅ YENİ YÖNTEM
 * import { AdaptiveColorPicker } from '@shared/color-system';
 *
 * <AdaptiveColorPicker
 *   selectedColor={selectedColor}
 *   selectedStyle={selectedStyle}
 *   onColorSelect={handleColorSelect}
 *   compact={true}
 * />
 * ```
 */

interface ColorPickerProps {
  /** Se<PERSON><PERSON> renk */
  selectedColor?: string;
  /** Seçili stil tipi */
  selectedStyle?: 'background' | 'text';
  /** Renk seçildiğinde tetiklenecek fonksiyon */
  onColorSelect: (color: string, style: 'background' | 'text') => void;
  /** Özel CSS sınıfı */
  className?: string;
  /** Compact görünüm */
  compact?: boolean;
  /** Açılım yönü - TextActionMenu ile uyumlu border için */
  placement?: 'above' | 'below';
}

const ColorPicker: React.FC<ColorPickerProps> = ({
  selectedColor,
  selectedStyle = 'background',
  onColorSelect,
  className = '',
  compact = false,
  placement = 'above'
}) => {
  const { bgColors, borderColors } = useReaderInteractionStyles();

  // 🎨 Yeni adaptive renk sistemi - geriye dönük uyumluluk için
  const { colors: adaptiveColors } = useColorPicker();

  // Eski format ile uyumluluk için dönüştür
  const colors = adaptiveColors.map(color => ({
    name: color.name,
    value: color.value
  }));

  // Profesyonel boyutlandırma sistemi
  const buttonSize = 28; // w-7 h-7 = 28px
  const gap = 8; // gap-2 = 8px
  const gridWidth = (buttonSize * 4) + (gap * 3); // 4 buton + 3 gap = 136px
  const containerPadding = 16; // p-4 = 16px
  const totalWidth = gridWidth + (containerPadding * 2); // 168px

  // Placement'a göre border radius ve border width ayarları
  const getBorderStyles = () => {
    if (!compact) return { borderRadius: '8px' };

    if (placement === 'below') {
      // ColorPicker aşağı açılıyor - sadece üst border yok, tüm köşeler yuvarlak
      return {
        borderRadius: '8px',
        borderTopWidth: '0'
      };
    } else {
      // ColorPicker yukarı açılıyor - sadece alt border yok, tüm köşeler yuvarlak
      return {
        borderRadius: '8px',
        borderBottomWidth: '0'
      };
    }
  };

  return (
    <div
      className={`color-picker p-3.5 border shadow-lg ${className}`}
      style={{
        backgroundColor: bgColors.menu, // TextActionMenu ile aynı overlay oranı (21%)
        borderColor: borderColors.default,
        width: compact ? `${totalWidth}px` : `${totalWidth + 20}px`, // Profesyonel hesaplama
        ...getBorderStyles() // Placement'a göre dinamik border
      }}
    >

      {/* Text color section */}
      <div className="mb-3">
        <h4 className="text-xs font-medium mb-1.5 opacity-70" style={{ color: 'var(--text-color)' }}>
          Text color
        </h4>
        <div className="flex gap-2 mb-1.5">
          {colors.slice(0, 4).map((color, index) => (
            <button
              key={`text-${index}`}
              className="relative w-7 h-7 rounded-lg border transition-all duration-200 hover:scale-110 flex items-center justify-center"
              style={{
                backgroundColor: bgColors.card,
                borderColor: selectedColor === color.value && selectedStyle === 'text' && selectedColor ? color.value : borderColors.light,
                borderWidth: selectedColor === color.value && selectedStyle === 'text' && selectedColor ? '2px' : '1px'
              }}
              onClick={() => onColorSelect(color.value, 'text')}
            >
              <span
                className="text-xs font-bold"
                style={{ color: color.value }}
              >
                A
              </span>
              {selectedColor === color.value && selectedStyle === 'text' && selectedColor && (
                <div className="absolute -top-0.5 -right-0.5 w-3 h-3 rounded-full bg-blue-500 flex items-center justify-center">
                  <Check size={7} className="text-white" />
                </div>
              )}
            </button>
          ))}
        </div>
        <div className="flex gap-2">
          {colors.slice(4, 8).map((color, index) => (
            <button
              key={`text-${index + 4}`}
              className="relative w-7 h-7 rounded-lg border transition-all duration-200 hover:scale-110 flex items-center justify-center"
              style={{
                backgroundColor: bgColors.card,
                borderColor: selectedColor === color.value && selectedStyle === 'text' && selectedColor ? color.value : borderColors.light,
                borderWidth: selectedColor === color.value && selectedStyle === 'text' && selectedColor ? '2px' : '1px'
              }}
              onClick={() => onColorSelect(color.value, 'text')}
            >
              <span
                className="text-xs font-bold"
                style={{ color: color.value }}
              >
                A
              </span>
              {selectedColor === color.value && selectedStyle === 'text' && selectedColor && (
                <div className="absolute -top-0.5 -right-0.5 w-3 h-3 rounded-full bg-blue-500 flex items-center justify-center">
                  <Check size={7} className="text-white" />
                </div>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Background color section */}
      <div>
        <h4 className="text-xs font-medium mb-1.5 opacity-70" style={{ color: 'var(--text-color)' }}>
          Background color
        </h4>
        <div className="flex gap-2 mb-1.5">
          {colors.slice(0, 4).map((color, index) => (
            <button
              key={`bg-${index}`}
              className="relative w-7 h-7 rounded-lg border-2 transition-all duration-200 hover:scale-110"
              style={{
                backgroundColor: color.value,
                borderColor: selectedColor === color.value && selectedStyle === 'background' && selectedColor ? '#ffffff' : 'transparent'
              }}
              onClick={() => onColorSelect(color.value, 'background')}
            >
              {selectedColor === color.value && selectedStyle === 'background' && selectedColor && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <Check size={12} className="text-white drop-shadow-sm" />
                </div>
              )}
            </button>
          ))}
        </div>
        <div className="flex gap-2">
          {colors.slice(4, 8).map((color, index) => (
            <button
              key={`bg-${index + 4}`}
              className="relative w-7 h-7 rounded-lg border-2 transition-all duration-200 hover:scale-110"
              style={{
                backgroundColor: color.value,
                borderColor: selectedColor === color.value && selectedStyle === 'background' && selectedColor ? '#ffffff' : 'transparent'
              }}
              onClick={() => onColorSelect(color.value, 'background')}
            >
              {selectedColor === color.value && selectedStyle === 'background' && selectedColor && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <Check size={12} className="text-white drop-shadow-sm" />
                </div>
              )}
            </button>
          ))}
        </div>

        {/* Temizle butonu - Grid genişliği ile uyumlu */}
        <div className="mt-2.5 pt-2.5 border-t" style={{ borderColor: borderColors.light }}>
          <button
            className="w-full flex items-center justify-center gap-2 py-1.5 rounded-lg border transition-all duration-200 hover:scale-105"
            style={{
              backgroundColor: bgColors.card,
              borderColor: borderColors.default,
              color: 'var(--text-color)',
              maxWidth: `${gridWidth}px` // Grid genişliği ile sınırla
            }}
            onClick={() => onColorSelect('DELETE', 'background')}
          >
            <Trash2 size={12} />
            <span className="text-xs font-medium">Temizle</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default memo(ColorPicker);
