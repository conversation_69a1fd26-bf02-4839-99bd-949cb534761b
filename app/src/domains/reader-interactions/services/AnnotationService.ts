import type { CreateAnnotationInput, Annotation, AnnotationFilters } from '@domains/reader-interactions/shared/types';
import { CombinedVerseData, ITranslator } from '@reader/models/types';

// Utility function for creating hash
const createHash = (str: string): string => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash |= 0; // 32-bit integer'a çevir
  }
  return Math.abs(hash).toString(36);
};

export interface SaveNoteInput {
  userId: string;
  verseKey: string;
  surahName?: string;
  noteContent: string;
  verseData: CombinedVerseData;
}

export interface SaveBookmarkInput {
  userId: string;
  verseKey: string;
  surahName?: string;
  verseData: CombinedVerseData;
  collection: { id: string; name: string };
  selectedMeals?: Array<{ id: string; enabled: boolean; name: string }>;
  availableTranslators?: ITranslator[];
  selectedTranslators?: string[];
}

export class AnnotationService {
  private static createAnnotation: ((input: CreateAnnotationInput) => Promise<Annotation | null>) | null = null;
  private static getAnnotations: ((filters?: AnnotationFilters) => Promise<Annotation[]>) | null = null;

  static initialize(createAnnotationFn: (input: CreateAnnotationInput) => Promise<Annotation | null>, getAnnotationsFn?: (filters?: AnnotationFilters) => Promise<Annotation[]>) {
    this.createAnnotation = createAnnotationFn;
    this.getAnnotations = getAnnotationsFn || null;
  }

  static async getNotes(userId: string, verseKey: string): Promise<Array<{
    id: string;
    content: string;
    createdAt: string;
    updatedAt?: string;
  }>> {
    try {
      if (!this.getAnnotations) {
        console.warn('AnnotationService.getNotes - getAnnotations function not initialized');
        return [];
      }

      const [surahId, verseNo] = verseKey.split('-');
      const sentenceId = `${surahId}_${verseNo}_0`;
      
      const result = await this.getAnnotations({
        user_id: userId,
        book_id: 'quran',
        section_id: surahId,
        sentence_id: sentenceId,
        annotation_type: 'note'
      });

      if (Array.isArray(result) && result.length > 0) {
        const mappedNotes = result.map((annotation: Annotation) => ({
          id: annotation.id,
          content: annotation.annotation_content || '',
          createdAt: annotation.created_at,
          updatedAt: annotation.updated_at
        }));
        return mappedNotes;
      }

      return [];
    } catch (error) {
      console.error('❌ AnnotationService.getNotes - error:', error);
      return [];
    }
  }

  static async saveNote(input: SaveNoteInput): Promise<{ success: boolean; message: string }> {
    try {
      const { userId, verseKey, surahName, noteContent } = input;
      
      if (!userId || !verseKey || !noteContent.trim()) {
        return { success: false, message: 'Not içeriği boş olamaz.' };
      }

      const [surahId, verseNo] = verseKey.split('-');
      const sentenceId = `${surahId}_${verseNo}_0`;
      const noteText = `${surahName || `Sure ${surahId}`}, ${verseNo}. Ayet`;
      
      const noteInput: CreateAnnotationInput = {
        user_id: userId,
        book_id: 'quran',
        section_id: surahId,
        sentence_id: [sentenceId],
        selection_start: 0,
        selection_end: noteText.length,
        selected_text: noteText,
        prefix_text: '',
        suffix_text: '',
        word_proximity: [],
        text_hash: createHash(noteText),
        sentence_hash: createHash(verseKey),
        annotation_type: 'note',
        annotation_content: noteContent.trim(),
        color: '#3b82f6',
        metadata: {
          surah_name: surahName,
          verse_number: verseNo,
          verse_key: verseKey,
          note_type: 'verse_reference'
        }
      };

      const result = await this.createAnnotation?.(noteInput);
      
      if (result) {
        return { success: true, message: 'Not başarıyla eklendi!' };
      } else {
        return { success: false, message: 'Not kaydedilirken hata oluştu.' };
      }
    } catch (error) {
      console.error('Note save error:', error);
      return { success: false, message: 'Beklenmeyen bir hata oluştu.' };
    }
  }

  static async saveBookmark(input: SaveBookmarkInput): Promise<{ success: boolean; message: string }> {
    try {
      const { 
        userId, 
        verseKey, 
        surahName, 
        verseData, 
        collection, 
        selectedMeals, 
        availableTranslators, 
        selectedTranslators 
      } = input;

      if (!userId || !verseKey || !verseData || !collection) {
        return { success: false, message: 'Yer imi eklemek için giriş yapmanız gerekiyor.' };
      }

      let fullVerseText = '';
      const savedContentTypes: string[] = [];

      // Build verse text based on selected meals or default translators
      if (selectedMeals) {
        selectedMeals.forEach((meal: { id: string; enabled: boolean; name: string }) => {
          if (!meal.enabled) return;
          if (meal.id === 'arabic') {
            if (verseData.arabic_text) {
              fullVerseText += verseData.arabic_text + '\n\n';
              savedContentTypes.push('arabic');
            }
          } else {
            const translator = availableTranslators?.find(t => String(t.id) === meal.id);
            const translationData = verseData.translations?.[meal.id];
            if (translator && translationData?.paragraphs) {
              fullVerseText += `[${translator.name}]\n${translationData.paragraphs.join('\n')}\n\n`;
              savedContentTypes.push(meal.id);
            }
          }
        });
      } else {
        fullVerseText = verseData.arabic_text || '';
        savedContentTypes.push('arabic');
        
        if (verseData.translations && availableTranslators && selectedTranslators) {
          const translationsText = selectedTranslators.map(translatorId => {
            const translator = availableTranslators.find(t => String(t.id) === translatorId);
            const translationData = verseData.translations?.[translatorId];
            if (translator && translationData?.paragraphs) {
              savedContentTypes.push(translatorId);
              return `[${translator.name}]\n${translationData.paragraphs.join('\n')}`;
            }
            return '';
          }).filter(Boolean).join('\n\n');
          
          if (translationsText) fullVerseText += `\n\n${translationsText}`;
        }
      }

      const [surahId, verseNo] = verseKey.split('-');
      const sentenceId = `${surahId}_${verseNo}_0`;
      
      const bookmarkInput: CreateAnnotationInput = {
        user_id: userId,
        book_id: 'quran',
        section_id: surahId,
        sentence_id: [sentenceId],
        selection_start: 0,
        selection_end: fullVerseText.trim().length,
        selected_text: fullVerseText.trim(),
        prefix_text: '',
        suffix_text: '',
        word_proximity: [],
        text_hash: createHash(fullVerseText.trim()),
        sentence_hash: createHash(verseKey),
        annotation_type: 'bookmark',
        collection_id: collection.id,
        color: '#10b981',
        metadata: {
          surah_name: surahName,
          verse_number: verseNo,
          verse_key: verseKey,
          saved_content_types: savedContentTypes,
          selected_meals: selectedMeals?.filter((m: { enabled: boolean }) => m.enabled).map((m: { id: string; name: string }) => ({ id: m.id, name: m.name })) || []
        }
      };

      const result = await this.createAnnotation?.(bookmarkInput);
      
      if (result) {
        return { 
          success: true, 
          message: `Ayet "${collection.name || 'koleksiyon'}"a kaydedildi!` 
        };
      } else {
        return { success: false, message: 'Yer imi kaydedilirken hata oluştu.' };
      }
    } catch (error) {
      console.error('Bookmark save error:', error);
      return { success: false, message: 'Beklenmeyen bir hata oluştu.' };
    }
  }
}
